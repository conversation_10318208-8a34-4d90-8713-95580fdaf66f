{"name": "@capacitor-community/sqlite", "version": "7.0.0", "description": "Community plugin for native & electron SQLite databases", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "src/", "ios/Plugin/", "electron/", "CapacitorCommunitySqlite.podspec"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/capacitor-community/sqlite.git"}, "devDependencies": {"@capacitor/android": "7.0.0", "@capacitor/cli": "7.0.0", "@capacitor/core": "7.0.0", "@capacitor/docgen": "0.3.0", "@capacitor/ios": "7.0.0", "@ionic/eslint-config": "0.4.0", "@ionic/prettier-config": "4.0.0", "@ionic/swiftlint-config": "2.0.0", "@rollup/plugin-commonjs": "28.0.2", "@rollup/plugin-node-resolve": "16.0.0", "eslint": "8.57.0", "prettier": "3.4.2", "prettier-plugin-java": "2.6.6", "rimraf": "6.0.1", "rollup": "4.30.1", "standard-version": "9.5.0", "swiftlint": "2.0.0", "typescript": "4.1.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}, "electron": {"src": "electron"}}, "dependencies": {"jeep-sqlite": "^2.7.2"}}
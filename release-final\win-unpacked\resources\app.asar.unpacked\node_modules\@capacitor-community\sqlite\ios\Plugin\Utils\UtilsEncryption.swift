//
//  UtilsEncryption.swift
//  Plugin
//
//  Created by  <PERSON><PERSON><PERSON> on 18/01/2021.
//  Copyright © 2021 Max <PERSON>. All rights reserved.
//

import Foundation
import SQLCipher

enum UtilsEncryptionError: Error {
    case encryptionFailed(message: String)
    case decryptionFailed(message: String)
}
class UtilsEncryption {

    // MARK: - EncryptDatabase

    // swiftlint:disable function_body_length
    class func encryptDatabase(databaseLocation: String, filePath: String,
                               password: String, version: Int) throws -> Bool {
        var ret: Bool = false
        var oDB: OpaquePointer?
        var eDB: OpaquePointer?
        do {
            if UtilsFile.isFileExist(filePath: filePath) {
                do {
                    let tempPath: String = try UtilsFile
                        .getFilePath(databaseLocation: databaseLocation,
                                     fileName: "temp.db")
                    try UtilsFile.renameFile(filePath: filePath,
                                             toFilePath: tempPath,
                                             databaseLocation: databaseLocation)
                    oDB = try UtilsSQLCipher
                        .openOrCreateDatabase(filename: tempPath,
                                              password: "",
                                              readonly: false)
                    eDB = try UtilsSQLCipher
                        .openOrCreateDatabase(filename: filePath,
                                              password: password,
                                              readonly: false)

                    var stmt: String = "ATTACH DATABASE '\(filePath)' "
                    stmt.append("AS encrypted KEY '\(password)';")
                    stmt.append("SELECT sqlcipher_export('encrypted');")
                    stmt.append("DETACH DATABASE encrypted;")
                    if sqlite3_exec(oDB, stmt, nil, nil, nil) ==
                        SQLITE_OK {
                        try _ = UtilsFile
                            .deleteFile(fileName: "temp.db",
                                        databaseLocation: databaseLocation)
                        // set the version
                        let sqltr: String = "PRAGMA user_version = \(version);"
                        if sqlite3_exec(eDB, sqltr, nil, nil, nil) != SQLITE_OK {
                            throw UtilsEncryptionError
                            .encryptionFailed(message: "set version to \(version) failed")
                        }

                        ret = true
                    }
                    // close the db
                    try UtilsSQLCipher.close(oDB: oDB)

                } catch UtilsFileError.getFilePathFailed {
                    throw UtilsEncryptionError
                    .encryptionFailed(message: "file path failed")
                } catch UtilsFileError.renameFileFailed {
                    throw UtilsEncryptionError
                    .encryptionFailed(message: "file rename failed")
                } catch UtilsSQLCipherError.openOrCreateDatabase(_) {
                    throw UtilsEncryptionError
                    .encryptionFailed(message: "open failed")
                } catch UtilsSQLCipherError.close(_) {
                    throw UtilsEncryptionError
                    .encryptionFailed(message: "close failed")
                } catch let error {
                    print("Error: \(error)")
                    throw UtilsEncryptionError
                    .encryptionFailed(message: "Error: \(error)")
                }
            }
        } catch let error {
            print("Error: \(error)")
            throw UtilsEncryptionError
            .encryptionFailed(message: "Error: \(error)")
        }
        return ret
    }

    // MARK: - DecryptDatabase

    // swiftlint:disable function_body_length
    class func decryptDatabase(databaseLocation: String, filePath: String,
                               password: String, version: Int) throws -> Bool {
        var ret: Bool = false
        var oDB: OpaquePointer?
        var eDB: OpaquePointer?
        do {
            if UtilsFile.isFileExist(filePath: filePath) {
                do {
                    let tempPath: String = try UtilsFile
                        .getFilePath(databaseLocation: databaseLocation,
                                     fileName: "temp.db")
                    try UtilsFile.renameFile(filePath: filePath,
                                             toFilePath: tempPath,
                                             databaseLocation: databaseLocation)
                    oDB = try UtilsSQLCipher
                        .openOrCreateDatabase(filename: tempPath,
                                              password: password,
                                              readonly: false)
                    eDB = try UtilsSQLCipher
                        .openOrCreateDatabase(filename: filePath,
                                              password: "",
                                              readonly: false)

                    var stmt: String = "PRAGMA key = '\(password)';"
                    stmt.append("ATTACH DATABASE '\(filePath)' ")
                    stmt.append("AS plaintext KEY '';")
                    stmt.append("SELECT sqlcipher_export('plaintext');")
                    stmt.append("DETACH DATABASE plaintext;")
                    if sqlite3_exec(oDB, stmt, nil, nil, nil) ==
                        SQLITE_OK {
                        try _ = UtilsFile
                            .deleteFile(fileName: "temp.db",
                                        databaseLocation: databaseLocation)
                        // set the version
                        let sqltr: String = "PRAGMA user_version = \(version);"
                        if sqlite3_exec(eDB, sqltr, nil, nil, nil) != SQLITE_OK {
                            throw UtilsEncryptionError
                            .decryptionFailed(message: "set version to \(version) failed")
                        }

                        ret = true
                    }
                    // close the db
                    try UtilsSQLCipher.close(oDB: oDB)

                } catch UtilsFileError.getFilePathFailed {
                    throw UtilsEncryptionError
                    .decryptionFailed(message: "file path failed")
                } catch UtilsFileError.renameFileFailed {
                    throw UtilsEncryptionError
                    .decryptionFailed(message: "file rename failed")
                } catch UtilsSQLCipherError.openOrCreateDatabase(_) {
                    throw UtilsEncryptionError
                    .decryptionFailed(message: "open failed")
                } catch UtilsSQLCipherError.close(_) {
                    throw UtilsEncryptionError
                    .decryptionFailed(message: "close failed")
                } catch let error {
                    print("Error: \(error)")
                    throw UtilsEncryptionError
                    .decryptionFailed(message: "Error: \(error)")
                }
            }
        } catch let error {
            print("Error: \(error)")
            throw UtilsEncryptionError
            .decryptionFailed(message: "Error: \(error)")
        }
        return ret
    }

}
// swiftlint:enable function_body_length

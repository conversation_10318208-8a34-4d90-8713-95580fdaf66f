directories:
  output: release-final
  buildResources: build
appId: com.smartboutique.app
productName: SmartBoutique
copyright: Copyright © 2024 SmartBoutique Team
files:
  - filter:
      - dist/**/*
      - dist-electron/**/*
      - node_modules/**/*
      - '!node_modules/**/*.{md,txt,LICENSE,CHANGELOG,README}'
      - '!node_modules/**/test/**/*'
      - '!node_modules/**/*.d.ts'
      - '!node_modules/**/docs/**/*'
      - '!node_modules/**/example/**/*'
      - '!node_modules/**/examples/**/*'
      - '!node_modules/**/*.map'
      - node_modules/better-sqlite3/**/*
extraResources:
  - from: assets
    to: assets
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  publisherName: SmartBoutique Team
  verifyUpdateCodeSignature: false
  requestedExecutionLevel: asInvoker
  artifactName: SmartBoutique-Setup-${version}.${ext}
nsis:
  oneClick: false
  perMachine: false
  allowToChangeInstallationDirectory: true
  deleteAppDataOnUninstall: false
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: SmartBoutique
  license: build/license.txt
  language: '1036'
  displayLanguageSelector: false
  artifactName: SmartBoutique-Installer-${version}.${ext}
portable:
  artifactName: SmartBoutique-Portable-${version}.${ext}
mac:
  target: dmg
linux:
  target: AppImage
extraMetadata:
  main: dist-electron/main.js
nodeGypRebuild: false
buildDependenciesFromSource: false
electronVersion: 28.3.3

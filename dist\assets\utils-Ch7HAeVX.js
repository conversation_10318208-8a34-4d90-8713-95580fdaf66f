function t(t){const e=Object.prototype.toString.call(t);return t instanceof Date||"object"==typeof t&&"[object Date]"===e?new t.constructor(+t):"number"==typeof t||"[object Number]"===e||"string"==typeof t||"[object String]"===e?new Date(t):new Date(NaN)}function e(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}function n(n,r){const a=t(n);return isNaN(r)?e(n,NaN):r?(a.setDate(a.getDate()+r),a):a}const r=6048e5;let a={};function o(){return a}function i(e,n){var r,a,i,u;const s=o(),c=(null==n?void 0:n.weekStartsOn)??(null==(a=null==(r=null==n?void 0:n.locale)?void 0:r.options)?void 0:a.weekStartsOn)??s.weekStartsOn??(null==(u=null==(i=s.locale)?void 0:i.options)?void 0:u.weekStartsOn)??0,d=t(e),l=d.getDay(),h=(l<c?7:0)+l-c;return d.setDate(d.getDate()-h),d.setHours(0,0,0,0),d}function u(t){return i(t,{weekStartsOn:1})}function s(n){const r=t(n),a=r.getFullYear(),o=e(n,0);o.setFullYear(a+1,0,4),o.setHours(0,0,0,0);const i=u(o),s=e(n,0);s.setFullYear(a,0,4),s.setHours(0,0,0,0);const c=u(s);return r.getTime()>=i.getTime()?a+1:r.getTime()>=c.getTime()?a:a-1}function c(e){const n=t(e);return n.setHours(0,0,0,0),n}function d(e){const n=t(e),r=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()));return r.setUTCFullYear(n.getFullYear()),+e-+r}function l(t,e){return n(t,7*e)}function h(e){if(!(n=e,n instanceof Date||"object"==typeof n&&"[object Date]"===Object.prototype.toString.call(n)||"number"==typeof e))return!1;var n;const r=t(e);return!isNaN(Number(r))}function f(e){const n=t(e);return n.setHours(23,59,59,999),n}function m(e){const n=t(e),r=n.getMonth();return n.setFullYear(n.getFullYear(),r+1,0),n.setHours(23,59,59,999),n}function g(e,n){const r=t(e.start),a=t(e.end);let o=+r>+a;const i=o?+r:+a,u=o?a:r;u.setHours(0,0,0,0);const s=[];for(;+u<=i;)s.push(t(u)),u.setDate(u.getDate()+1),u.setHours(0,0,0,0);return o?s.reverse():s}function w(e,n){const r=t(e.start),a=t(e.end);let o=+r>+a;const i=o?+r:+a,u=o?a:r;u.setHours(0,0,0,0),u.setDate(1);const s=[];for(;+u<=i;)s.push(t(u)),u.setMonth(u.getMonth()+1);return o?s.reverse():s}function b(e,n){const r=t(e.start),a=t(e.end);let o=+r>+a;const u=i(o?a:r,n),s=i(o?r:a,n);u.setHours(15),s.setHours(15);const c=+s.getTime();let d=u,h=(null==n?void 0:n.step)??1;if(!h)return[];h<0&&(h=-h,o=!o);const f=[];for(;+d<=c;)d.setHours(0),f.push(t(d)),d=l(d,h),d.setHours(15);return o?f.reverse():f}function y(e){const n=t(e);return n.setDate(1),n.setHours(0,0,0,0),n}function v(e,n){const r=null==n?void 0:n.weekStartsOn,a=t(e),o=a.getDay(),i=6+(o<r?-7:0)-(o-r);return a.setDate(a.getDate()+i),a.setHours(23,59,59,999),a}const p={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function M(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const k={date:M({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:M({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:M({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},x={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function D(t){return(e,n)=>{let r;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&t.formattingValues){const e=t.defaultFormattingWidth||t.defaultWidth,a=(null==n?void 0:n.width)?String(n.width):e;r=t.formattingValues[a]||t.formattingValues[e]}else{const e=t.defaultWidth,a=(null==n?void 0:n.width)?String(n.width):t.defaultWidth;r=t.values[a]||t.values[e]}return r[t.argumentCallback?t.argumentCallback(e):e]}}function P(t){return(e,n={})=>{const r=n.width,a=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],o=e.match(a);if(!o)return null;const i=o[0],u=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],s=Array.isArray(u)?function(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n;return}(u,t=>t.test(i)):function(t,e){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n;return}(u,t=>t.test(i));let c;c=t.valueCallback?t.valueCallback(s):s,c=n.valueCallback?n.valueCallback(c):c;return{value:c,rest:e.slice(i.length)}}}function S(t){return(e,n={})=>{const r=e.match(t.matchPattern);if(!r)return null;const a=r[0],o=e.match(t.parsePattern);if(!o)return null;let i=t.valueCallback?t.valueCallback(o[0]):o[0];i=n.valueCallback?n.valueCallback(i):i;return{value:i,rest:e.slice(a.length)}}}const W={code:"en-US",formatDistance:(t,e,n)=>{let r;const a=p[t];return r="string"==typeof a?a:1===e?a.one:a.other.replace("{{count}}",e.toString()),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:k,formatRelative:(t,e,n,r)=>x[t],localize:{ordinalNumber:(t,e)=>{const n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:D({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:D({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:D({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:D({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:D({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:S({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:P({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:P({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:P({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:P({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:P({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function T(n){const r=t(n),a=function(t,e){const n=c(t),r=c(e),a=+n-d(n),o=+r-d(r);return Math.round((a-o)/864e5)}(r,function(n){const r=t(n),a=e(n,0);return a.setFullYear(r.getFullYear(),0,1),a.setHours(0,0,0,0),a}(r));return a+1}function Y(n){const a=t(n),o=+u(a)-+function(t){const n=s(t),r=e(t,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),u(r)}(a);return Math.round(o/r)+1}function H(n,r){var a,u,s,c;const d=t(n),l=d.getFullYear(),h=o(),f=(null==r?void 0:r.firstWeekContainsDate)??(null==(u=null==(a=null==r?void 0:r.locale)?void 0:a.options)?void 0:u.firstWeekContainsDate)??h.firstWeekContainsDate??(null==(c=null==(s=h.locale)?void 0:s.options)?void 0:c.firstWeekContainsDate)??1,m=e(n,0);m.setFullYear(l+1,0,f),m.setHours(0,0,0,0);const g=i(m,r),w=e(n,0);w.setFullYear(l,0,f),w.setHours(0,0,0,0);const b=i(w,r);return d.getTime()>=g.getTime()?l+1:d.getTime()>=b.getTime()?l:l-1}function C(n,a){const u=t(n),s=+i(u,a)-+function(t,n){var r,a,u,s;const c=o(),d=(null==n?void 0:n.firstWeekContainsDate)??(null==(a=null==(r=null==n?void 0:n.locale)?void 0:r.options)?void 0:a.firstWeekContainsDate)??c.firstWeekContainsDate??(null==(s=null==(u=c.locale)?void 0:u.options)?void 0:s.firstWeekContainsDate)??1,l=H(t,n),h=e(t,0);return h.setFullYear(l,0,d),h.setHours(0,0,0,0),i(h,n)}(u,a);return Math.round(s/r)+1}function N(t,e){return(t<0?"-":"")+Math.abs(t).toString().padStart(e,"0")}const O={y(t,e){const n=t.getFullYear(),r=n>0?n:1-n;return N("yy"===e?r%100:r,e.length)},M(t,e){const n=t.getMonth();return"M"===e?String(n+1):N(n+1,2)},d:(t,e)=>N(t.getDate(),e.length),a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>N(t.getHours()%12||12,e.length),H:(t,e)=>N(t.getHours(),e.length),m:(t,e)=>N(t.getMinutes(),e.length),s:(t,e)=>N(t.getSeconds(),e.length),S(t,e){const n=e.length,r=t.getMilliseconds();return N(Math.trunc(r*Math.pow(10,n-3)),e.length)}},q="midnight",F="noon",j="morning",E="afternoon",z="evening",L="night",Q={G:function(t,e,n){const r=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){const e=t.getFullYear(),r=e>0?e:1-e;return n.ordinalNumber(r,{unit:"year"})}return O.y(t,e)},Y:function(t,e,n,r){const a=H(t,r),o=a>0?a:1-a;if("YY"===e){return N(o%100,2)}return"Yo"===e?n.ordinalNumber(o,{unit:"year"}):N(o,e.length)},R:function(t,e){return N(s(t),e.length)},u:function(t,e){return N(t.getFullYear(),e.length)},Q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return N(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return N(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){const r=t.getMonth();switch(e){case"M":case"MM":return O.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){const r=t.getMonth();switch(e){case"L":return String(r+1);case"LL":return N(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){const a=C(t,r);return"wo"===e?n.ordinalNumber(a,{unit:"week"}):N(a,e.length)},I:function(t,e,n){const r=Y(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):N(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):O.d(t,e)},D:function(t,e,n){const r=T(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):N(r,e.length)},E:function(t,e,n){const r=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){const a=t.getDay(),o=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return N(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){const a=t.getDay(),o=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return N(o,e.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){const r=t.getDay(),a=0===r?7:r;switch(e){case"i":return String(a);case"ii":return N(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){const r=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){const r=t.getHours();let a;switch(a=12===r?F:0===r?q:r/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,e,n){const r=t.getHours();let a;switch(a=r>=17?z:r>=12?E:r>=4?j:L,e){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return O.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):O.H(t,e)},K:function(t,e,n){const r=t.getHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):N(r,e.length)},k:function(t,e,n){let r=t.getHours();return 0===r&&(r=24),"ko"===e?n.ordinalNumber(r,{unit:"hour"}):N(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):O.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):O.s(t,e)},S:function(t,e){return O.S(t,e)},X:function(t,e,n){const r=t.getTimezoneOffset();if(0===r)return"Z";switch(e){case"X":return G(r);case"XXXX":case"XX":return X(r);default:return X(r,":")}},x:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"x":return G(r);case"xxxx":case"xx":return X(r);default:return X(r,":")}},O:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+A(r,":");default:return"GMT"+X(r,":")}},z:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+A(r,":");default:return"GMT"+X(r,":")}},t:function(t,e,n){return N(Math.trunc(t.getTime()/1e3),e.length)},T:function(t,e,n){return N(t.getTime(),e.length)}};function A(t,e=""){const n=t>0?"-":"+",r=Math.abs(t),a=Math.trunc(r/60),o=r%60;return 0===o?n+String(a):n+String(a)+e+N(o,2)}function G(t,e){if(t%60==0){return(t>0?"-":"+")+N(Math.abs(t)/60,2)}return X(t,e)}function X(t,e=""){const n=t>0?"-":"+",r=Math.abs(t);return n+N(Math.trunc(r/60),2)+e+N(r%60,2)}const B=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},$=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},J={p:$,P:(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],r=n[1],a=n[2];if(!a)return B(t,e);let o;switch(r){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;default:o=e.dateTime({width:"full"})}return o.replace("{{date}}",B(r,e)).replace("{{time}}",$(a,e))}},I=/^D+$/,R=/^Y+$/,U=["D","DD","YY","YYYY"];const V=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,K=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Z=/^'([^]*?)'?$/,_=/''/g,tt=/[a-zA-Z]/;function et(e,n,r){var a,i,u,s,c,d,l,f;const m=o(),g=(null==r?void 0:r.locale)??m.locale??W,w=(null==r?void 0:r.firstWeekContainsDate)??(null==(i=null==(a=null==r?void 0:r.locale)?void 0:a.options)?void 0:i.firstWeekContainsDate)??m.firstWeekContainsDate??(null==(s=null==(u=m.locale)?void 0:u.options)?void 0:s.firstWeekContainsDate)??1,b=(null==r?void 0:r.weekStartsOn)??(null==(d=null==(c=null==r?void 0:r.locale)?void 0:c.options)?void 0:d.weekStartsOn)??m.weekStartsOn??(null==(f=null==(l=m.locale)?void 0:l.options)?void 0:f.weekStartsOn)??0,y=t(e);if(!h(y))throw new RangeError("Invalid time value");let v=n.match(K).map(t=>{const e=t[0];if("p"===e||"P"===e){return(0,J[e])(t,g.formatLong)}return t}).join("").match(V).map(t=>{if("''"===t)return{isToken:!1,value:"'"};const e=t[0];if("'"===e)return{isToken:!1,value:nt(t)};if(Q[e])return{isToken:!0,value:t};if(e.match(tt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}});g.localize.preprocessor&&(v=g.localize.preprocessor(y,v));const p={firstWeekContainsDate:w,weekStartsOn:b,locale:g};return v.map(t=>{if(!t.isToken)return t.value;const a=t.value;(!(null==r?void 0:r.useAdditionalWeekYearTokens)&&function(t){return R.test(t)}(a)||!(null==r?void 0:r.useAdditionalDayOfYearTokens)&&function(t){return I.test(t)}(a))&&function(t,e,n){const r=function(t,e,n){const r="Y"===t[0]?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(t,e,n);if(console.warn(r),U.includes(t))throw new RangeError(r)}(a,n,String(e));return(0,Q[a[0]])(y,a,g.localize,p)}).join("")}function nt(t){const e=t.match(Z);return e?e[1].replace(_,"'"):t}function rt(e,n){const r=t(e),a=t(n);return r.getTime()>a.getTime()}function at(e,n){return+t(e)<+t(n)}function ot(e,n){const r=+t(e),[a,o]=[+t(n.start),+t(n.end)].sort((t,e)=>t-e);return r>=a&&r<=o}function it(t,e){return n(t,-e)}export{D as a,M as b,P as c,S as d,f as e,et as f,it as g,at as h,rt as i,y as j,m as k,h as l,ot as m,g as n,b as o,v as p,w as q,c as s};

{"name": "@capacitor/android", "version": "7.3.0", "description": "Capacitor: Cross-platform apps with JavaScript and the web", "homepage": "https://capacitorjs.com", "author": "Ionic Team <<EMAIL>> (https://ionic.io)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/capacitor.git"}, "files": ["capacitor/build.gradle", "capacitor/lint-baseline.xml", "capacitor/lint.xml", "capacitor/proguard-rules.pro", "capacitor/src/main/"], "peerDependencies": {"@capacitor/core": "^7.3.0"}, "publishConfig": {"access": "public"}}
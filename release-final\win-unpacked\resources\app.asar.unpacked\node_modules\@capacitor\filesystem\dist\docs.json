{"api": {"name": "FilesystemPlugin", "slug": "filesystemplugin", "docs": "", "tags": [], "methods": [{"name": "checkPermissions", "signature": "() => Promise<PermissionStatus>", "parameters": [], "returns": "Promise<PermissionStatus>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Check read/write permissions.\nRequired on Android, only when using `Directory.Documents` or\n`Directory.ExternalStorage`.", "complexTypes": ["PermissionStatus"], "slug": "checkpermissions"}, {"name": "requestPermissions", "signature": "() => Promise<PermissionStatus>", "parameters": [], "returns": "Promise<PermissionStatus>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Request read/write permissions.\nRequired on Android, only when using `Directory.Documents` or\n`Directory.ExternalStorage`.", "complexTypes": ["PermissionStatus"], "slug": "requestpermissions"}, {"name": "readFile", "signature": "(options: ReadFileOptions) => Promise<ReadFileResult>", "parameters": [{"name": "options", "docs": "", "type": "ReadFileOptions"}], "returns": "Promise<ReadFileResult>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Read a file from disk", "complexTypes": ["ReadFileResult", "ReadFileOptions"], "slug": "readfile"}, {"name": "readFileInChunks", "signature": "(options: ReadFileInChunksOptions, callback: ReadFileInChunksCallback) => Promise<CallbackID>", "parameters": [{"name": "options", "docs": "", "type": "ReadFileInChunksOptions"}, {"name": "callback", "docs": "", "type": "ReadFileInChunksCallback"}], "returns": "Promise<string>", "tags": [{"name": "since", "text": "7.1.0"}], "docs": "Read a file from disk, in chunks.\nNative only (not available in web).\nUse the callback to receive each read chunk.\nIf empty chunk is returned, it means file has been completely read.", "complexTypes": ["ReadFileInChunksOptions", "ReadFileInChunksCallback", "CallbackID"], "slug": "read<PERSON>lein<PERSON><PERSON>"}, {"name": "writeFile", "signature": "(options: WriteFileOptions) => Promise<WriteFileResult>", "parameters": [{"name": "options", "docs": "", "type": "WriteFileOptions"}], "returns": "Promise<WriteFileResult>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Write a file to disk in the specified location on device", "complexTypes": ["WriteFileResult", "WriteFileOptions"], "slug": "writefile"}, {"name": "appendFile", "signature": "(options: AppendFileOptions) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "AppendFileOptions"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Append to a file on disk in the specified location on device", "complexTypes": ["AppendFileOptions"], "slug": "appendfile"}, {"name": "deleteFile", "signature": "(options: DeleteFileOptions) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "DeleteFileOptions"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Delete a file from disk", "complexTypes": ["DeleteFileOptions"], "slug": "deletefile"}, {"name": "mkdir", "signature": "(options: MkdirOptions) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "MkdirOptions"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Create a directory.", "complexTypes": ["MkdirOptions"], "slug": "mkdir"}, {"name": "rmdir", "signature": "(options: RmdirOptions) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "RmdirOptions"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Remove a directory", "complexTypes": ["RmdirOptions"], "slug": "rmdir"}, {"name": "readdir", "signature": "(options: ReaddirOptions) => Promise<ReaddirResult>", "parameters": [{"name": "options", "docs": "", "type": "ReaddirOptions"}], "returns": "Promise<ReaddirResult>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Return a list of files from the directory (not recursive)", "complexTypes": ["ReaddirResult", "ReaddirOptions"], "slug": "readdir"}, {"name": "get<PERSON><PERSON>", "signature": "(options: GetUriOptions) => Promise<GetUriResult>", "parameters": [{"name": "options", "docs": "", "type": "GetUriOptions"}], "returns": "Promise<GetUriResult>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Return full File URI for a path and directory", "complexTypes": ["GetUriResult", "GetUriOptions"], "slug": "geturi"}, {"name": "stat", "signature": "(options: StatOptions) => Promise<StatResult>", "parameters": [{"name": "options", "docs": "", "type": "StatOptions"}], "returns": "Promise<FileInfo>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Return data about a file", "complexTypes": ["FileInfo", "StatOptions", "StatResult"], "slug": "stat"}, {"name": "rename", "signature": "(options: RenameOptions) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "CopyOptions"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Rename a file or directory", "complexTypes": ["RenameOptions"], "slug": "rename"}, {"name": "copy", "signature": "(options: CopyOptions) => Promise<CopyResult>", "parameters": [{"name": "options", "docs": "", "type": "CopyOptions"}], "returns": "Promise<CopyResult>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Copy a file or directory", "complexTypes": ["Copy<PERSON><PERSON>ult", "CopyOptions"], "slug": "copy"}, {"name": "downloadFile", "signature": "(options: DownloadFileOptions) => Promise<DownloadFileResult>", "parameters": [{"name": "options", "docs": "", "type": "DownloadFileOptions"}], "returns": "Promise<DownloadFileResult>", "tags": [{"name": "since", "text": "5.1.0"}, {"name": "deprecated", "text": "Use the"}, {"name": "capacitor", "text": "/file-transfer plugin instead."}], "docs": "Perform a http request to a server and download the file to the specified destination.\n\nThis method has been deprecated since version 7.1.0.\nWe recommend using the @capacitor/file-transfer plugin instead, in conjunction with this plugin.", "complexTypes": ["DownloadFileResult", "DownloadFileOptions"], "slug": "downloadfile"}, {"name": "addListener", "signature": "(eventName: 'progress', listenerFunc: ProgressListener) => Promise<PluginListenerHandle>", "parameters": [{"name": "eventName", "docs": "", "type": "'progress'"}, {"name": "listenerFunc", "docs": "", "type": "ProgressListener"}], "returns": "Promise<PluginListenerHandle>", "tags": [{"name": "since", "text": "5.1.0"}, {"name": "deprecated", "text": "Use the"}, {"name": "capacitor", "text": "/file-transfer plugin instead."}], "docs": "Add a listener to file download progress events.\n\nThis method has been deprecated since version 7.1.0.\nWe recommend using the @capacitor/file-transfer plugin instead, in conjunction with this plugin.", "complexTypes": ["PluginListenerHandle", "ProgressListener"], "slug": "addlistenerprogress-"}, {"name": "removeAllListeners", "signature": "() => Promise<void>", "parameters": [], "returns": "Promise<void>", "tags": [{"name": "since", "text": "5.2.0"}, {"name": "deprecated", "text": "Use the"}, {"name": "capacitor", "text": "/file-transfer plugin instead."}], "docs": "Remove all listeners for this plugin.\n\nThis method has been deprecated since version 7.1.0.\nWe recommend using the @capacitor/file-transfer plugin instead, in conjunction with this plugin.", "complexTypes": [], "slug": "removealllisteners"}], "properties": []}, "interfaces": [{"name": "PermissionStatus", "slug": "permission<PERSON>tus", "docs": "", "tags": [], "methods": [], "properties": [{"name": "publicStorage", "tags": [], "docs": "", "complexTypes": ["PermissionState"], "type": "PermissionState"}]}, {"name": "ReadFileResult", "slug": "readfileresult", "docs": "", "tags": [], "methods": [], "properties": [{"name": "data", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The representation of the data contained in the file\n\nNote: Blob is only available on Web. On native, the data is returned as a string.", "complexTypes": ["Blob"], "type": "string | Blob"}]}, {"name": "ReadFileOptions", "slug": "readfileoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the file to read", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to read the file from", "complexTypes": ["Directory"], "type": "Directory"}, {"name": "encoding", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The encoding to read the file in, if not provided, data\nis read as binary and returned as base64 encoded.\n\nPass Encoding.UTF8 to read data as string", "complexTypes": ["Encoding"], "type": "Encoding"}]}, {"name": "ReadFileInChunksOptions", "slug": "readfileinchunksoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "chunkSize", "tags": [{"text": "7.1.0", "name": "since"}], "docs": "Size of the chunks in bytes.", "complexTypes": [], "type": "number"}]}, {"name": "WriteFileResult", "slug": "writefileresult", "docs": "", "tags": [], "methods": [], "properties": [{"name": "uri", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The uri where the file was written into", "complexTypes": [], "type": "string"}]}, {"name": "WriteFileOptions", "slug": "writefileoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the file to write", "complexTypes": [], "type": "string"}, {"name": "data", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The data to write\n\nNote: Blob data is only supported on Web.", "complexTypes": ["Blob"], "type": "string | Blob"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to store the file in", "complexTypes": ["Directory"], "type": "Directory"}, {"name": "encoding", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The encoding to write the file in. If not provided, data\nis written as base64 encoded.\n\nPass Encoding.UTF8 to write data as string", "complexTypes": ["Encoding"], "type": "Encoding"}, {"name": "recursive", "tags": [{"text": "false", "name": "default"}, {"text": "1.0.0", "name": "since"}], "docs": "Whether to create any missing parent directories.", "complexTypes": [], "type": "boolean | undefined"}]}, {"name": "AppendFileOptions", "slug": "appendfileoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the file to append", "complexTypes": [], "type": "string"}, {"name": "data", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The data to write", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to store the file in", "complexTypes": ["Directory"], "type": "Directory"}, {"name": "encoding", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The encoding to write the file in. If not provided, data\nis written as base64 encoded.\n\nPass Encoding.UTF8 to write data as string", "complexTypes": ["Encoding"], "type": "Encoding"}]}, {"name": "DeleteFileOptions", "slug": "deletefileoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the file to delete", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to delete the file from", "complexTypes": ["Directory"], "type": "Directory"}]}, {"name": "MkdirOptions", "slug": "mkdiroptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the new directory", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to make the new directory in", "complexTypes": ["Directory"], "type": "Directory"}, {"name": "recursive", "tags": [{"text": "false", "name": "default"}, {"text": "1.0.0", "name": "since"}], "docs": "Whether to create any missing parent directories as well.", "complexTypes": [], "type": "boolean | undefined"}]}, {"name": "RmdirOptions", "slug": "rmdiroptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the directory to remove", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to remove the directory from", "complexTypes": ["Directory"], "type": "Directory"}, {"name": "recursive", "tags": [{"text": "false", "name": "default"}, {"text": "1.0.0", "name": "since"}], "docs": "Whether to recursively remove the contents of the directory", "complexTypes": [], "type": "boolean | undefined"}]}, {"name": "ReaddirResult", "slug": "readdirresult", "docs": "", "tags": [], "methods": [], "properties": [{"name": "files", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "List of files and directories inside the directory", "complexTypes": ["FileInfo"], "type": "FileInfo[]"}]}, {"name": "FileInfo", "slug": "fileinfo", "docs": "", "tags": [], "methods": [], "properties": [{"name": "name", "tags": [{"text": "7.1.0", "name": "since"}], "docs": "Name of the file or directory.", "complexTypes": [], "type": "string"}, {"name": "type", "tags": [{"text": "4.0.0", "name": "since"}], "docs": "Type of the file.", "complexTypes": [], "type": "'file' | 'directory'"}, {"name": "size", "tags": [{"text": "4.0.0", "name": "since"}], "docs": "Size of the file in bytes.", "complexTypes": [], "type": "number"}, {"name": "ctime", "tags": [{"text": "7.1.0", "name": "since"}], "docs": "Time of creation in milliseconds.\n\nIt's not available on Android 7 and older devices.", "complexTypes": [], "type": "number | undefined"}, {"name": "mtime", "tags": [{"text": "7.1.0", "name": "since"}], "docs": "Time of last modification in milliseconds.", "complexTypes": [], "type": "number"}, {"name": "uri", "tags": [{"text": "4.0.0", "name": "since"}], "docs": "The uri of the file.", "complexTypes": [], "type": "string"}]}, {"name": "ReaddirOptions", "slug": "readdiroptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the directory to read", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to list files from", "complexTypes": ["Directory"], "type": "Directory"}]}, {"name": "GetUriResult", "slug": "<PERSON><PERSON><PERSON><PERSON>", "docs": "", "tags": [], "methods": [], "properties": [{"name": "uri", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The uri of the file", "complexTypes": [], "type": "string"}]}, {"name": "GetUriOptions", "slug": "geturioptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the file to get the URI for", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to get the file under", "complexTypes": ["Directory"], "type": "Directory"}]}, {"name": "StatOptions", "slug": "statoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The path of the file to get data about", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` to get the file under", "complexTypes": ["Directory"], "type": "Directory"}]}, {"name": "CopyOptions", "slug": "copyoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "from", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The existing file or directory", "complexTypes": [], "type": "string"}, {"name": "to", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The destination file or directory", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` containing the existing file or directory", "complexTypes": ["Directory"], "type": "Directory"}, {"name": "toDirectory", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The `Directory` containing the destination file or directory. If not supplied will use the 'directory'\nparameter as the destination", "complexTypes": ["Directory"], "type": "Directory"}]}, {"name": "Copy<PERSON><PERSON>ult", "slug": "copyresult", "docs": "", "tags": [], "methods": [], "properties": [{"name": "uri", "tags": [{"text": "4.0.0", "name": "since"}], "docs": "The uri where the file was copied into", "complexTypes": [], "type": "string"}]}, {"name": "DownloadFileResult", "slug": "downloadfileresult", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "5.1.0", "name": "since"}], "docs": "The path the file was downloaded to.", "complexTypes": [], "type": "string | undefined"}, {"name": "blob", "tags": [{"text": "5.1.0", "name": "since"}], "docs": "The blob data of the downloaded file.\nThis is only available on web.", "complexTypes": ["Blob"], "type": "Blob"}]}, {"name": "DownloadFileOptions", "slug": "downloadfileoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "path", "tags": [{"text": "5.1.0", "name": "since"}], "docs": "The path the downloaded file should be moved to.", "complexTypes": [], "type": "string"}, {"name": "directory", "tags": [{"text": "5.1.0", "name": "since"}], "docs": "The directory to write the file to.\nIf this option is used, filePath can be a relative path rather than absolute.\nThe default is the `DATA` directory.", "complexTypes": ["Directory"], "type": "Directory"}, {"name": "progress", "tags": [{"text": "5.1.0", "name": "since"}], "docs": "An optional listener function to receive downloaded progress events.\nIf this option is used, progress event should be dispatched on every chunk received.\nChunks are throttled to every 100ms on Android/iOS to avoid slowdowns.", "complexTypes": [], "type": "boolean | undefined"}, {"name": "recursive", "tags": [{"text": "false", "name": "default"}, {"text": "5.1.2", "name": "since"}], "docs": "Whether to create any missing parent directories.", "complexTypes": [], "type": "boolean | undefined"}]}, {"name": "PluginListenerHandle", "slug": "pluginlistenerhandle", "docs": "", "tags": [], "methods": [], "properties": [{"name": "remove", "tags": [], "docs": "", "complexTypes": [], "type": "() => Promise<void>"}]}, {"name": "ProgressStatus", "slug": "progressstatus", "docs": "", "tags": [], "methods": [], "properties": [{"name": "url", "tags": [{"text": "5.1.0", "name": "since"}], "docs": "The url of the file being downloaded.", "complexTypes": [], "type": "string"}, {"name": "bytes", "tags": [{"text": "5.1.0", "name": "since"}], "docs": "The number of bytes downloaded so far.", "complexTypes": [], "type": "number"}, {"name": "contentLength", "tags": [{"text": "5.1.0", "name": "since"}], "docs": "The total number of bytes to download for this file.", "complexTypes": [], "type": "number"}]}], "enums": [{"name": "Directory", "slug": "directory", "members": [{"name": "Documents", "value": "'DOCUMENTS'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The Documents directory.\nOn iOS it's the app's documents directory.\nUse this directory to store user-generated content.\nOn Android it's the Public Documents folder, so it's accessible from other apps.\nIt's not accessible on Android 10 unless the app enables legacy External Storage\nby adding `android:requestLegacyExternalStorage=\"true\"` in the `application` tag\nin the `AndroidManifest.xml`.\nOn Android 11 or newer the app can only access the files/folders the app created."}, {"name": "Data", "value": "'DATA'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The Data directory.\nOn iOS it will use the Documents directory.\nOn Android it's the directory holding application files.\nFiles will be deleted when the application is uninstalled."}, {"name": "Library", "value": "'LIBRARY'", "tags": [{"text": "1.1.0", "name": "since"}], "docs": "The Library directory.\nOn iOS it will use the Library directory.\nOn Android it's the directory holding application files.\nFiles will be deleted when the application is uninstalled."}, {"name": "<PERSON><PERSON>", "value": "'CACHE'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The Cache directory.\nCan be deleted in cases of low memory, so use this directory to write app-specific files.\nthat your app can re-create easily."}, {"name": "External", "value": "'EXTERNAL'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The external directory.\nOn iOS it will use the Documents directory.\nOn Android it's the directory on the primary shared/external\nstorage device where the application can place persistent files it owns.\nThese files are internal to the applications, and not typically visible\nto the user as media.\nFiles will be deleted when the application is uninstalled."}, {"name": "ExternalStorage", "value": "'EXTERNAL_STORAGE'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The external storage directory.\nOn iOS it will use the Documents directory.\nOn Android it's the primary shared/external storage directory.\nIt's not accessible on Android 10 unless the app enables legacy External Storage\nby adding `android:requestLegacyExternalStorage=\"true\"` in the `application` tag\nin the `AndroidManifest.xml`.\nIt's not accessible on Android 11 or newer."}, {"name": "ExternalCache", "value": "'EXTERNAL_CACHE'", "tags": [{"text": "7.1.0", "name": "since"}], "docs": "The external cache directory.\nOn iOS it will use the Documents directory.\nOn Android it's the primary shared/external cache."}, {"name": "LibraryNoCloud", "value": "'LIBRARY_NO_CLOUD'", "tags": [{"text": "7.1.0", "name": "since"}], "docs": "The Library directory without cloud backup. Used in iOS.\nOn Android it's the directory holding application files."}, {"name": "Temporary", "value": "'TEMPORARY'", "tags": [{"text": "7.1.0", "name": "since"}], "docs": "A temporary directory for iOS.\nOn Android it's the directory holding the application cache."}]}, {"name": "Encoding", "slug": "encoding", "members": [{"name": "UTF8", "value": "'utf8'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "Eight-bit UCS Transformation Format"}, {"name": "ASCII", "value": "'ascii'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "Seven-bit ASCII, a.k.a. ISO646-US, a.k.a. the Basic Latin block of the\nUnicode character set\nThis encoding is only supported on Android."}, {"name": "UTF16", "value": "'utf16'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "Sixteen-bit UCS Transformation Format, byte order identified by an\noptional byte-order mark\nThis encoding is only supported on Android."}]}], "typeAliases": [{"name": "PermissionState", "slug": "permissionstate", "docs": "", "types": [{"text": "'prompt'", "complexTypes": []}, {"text": "'prompt-with-rationale'", "complexTypes": []}, {"text": "'granted'", "complexTypes": []}, {"text": "'denied'", "complexTypes": []}]}, {"name": "ReadFileInChunksCallback", "slug": "readfilein<PERSON><PERSON><PERSON>lback", "docs": "Callback for receiving chunks read from a file, or error if something went wrong.", "types": [{"text": "(chunkRead: ReadFileResult | null, err?: any): void", "complexTypes": ["ReadFileResult"]}]}, {"name": "CallbackID", "slug": "callbackid", "docs": "", "types": [{"text": "string", "complexTypes": []}]}, {"name": "StatResult", "slug": "statresult", "docs": "", "types": [{"text": "FileInfo", "complexTypes": ["FileInfo"]}]}, {"name": "RenameOptions", "slug": "renameoptions", "docs": "", "types": [{"text": "CopyOptions", "complexTypes": ["CopyOptions"]}]}, {"name": "ProgressListener", "slug": "progresslistener", "docs": "A listener function that receives progress events.", "types": [{"text": "(progress: ProgressStatus): void", "complexTypes": ["ProgressStatus"]}]}], "pluginConfigs": []}
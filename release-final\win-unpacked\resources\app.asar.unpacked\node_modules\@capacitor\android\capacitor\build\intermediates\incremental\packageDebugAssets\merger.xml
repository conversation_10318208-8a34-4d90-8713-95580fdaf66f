<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\My Software Projects\SmartBoutique\node_modules\@capacitor\android\capacitor\src\main\assets"><file name="native-bridge.js" path="C:\My Software Projects\SmartBoutique\node_modules\@capacitor\android\capacitor\src\main\assets\native-bridge.js"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\My Software Projects\SmartBoutique\node_modules\@capacitor\android\capacitor\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\My Software Projects\SmartBoutique\node_modules\@capacitor\android\capacitor\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>
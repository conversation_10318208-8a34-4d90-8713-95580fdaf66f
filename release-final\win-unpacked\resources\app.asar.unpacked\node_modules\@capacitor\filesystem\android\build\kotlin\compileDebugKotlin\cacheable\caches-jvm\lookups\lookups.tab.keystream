  Manifest android  
permission android.Manifest  READ_EXTERNAL_STORAGE android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  Context android.content  applicationContext android.content.Context  cacheDir android.content.Context  filesDir android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getCACHEDir android.content.Context  getCacheDir android.content.Context  getExternalFilesDir android.content.Context  getFILESDir android.content.Context  getFilesDir android.content.Context  setApplicationContext android.content.Context  setCacheDir android.content.Context  setFilesDir android.content.Context  MediaScannerConnection 
android.media  scanFile $android.media.MediaScannerConnection  Uri android.net  getPATH android.net.Uri  getPath android.net.Uri  	getSCHEME android.net.Uri  	getScheme android.net.Uri  parse android.net.Uri  path android.net.Uri  scheme android.net.Uri  setPath android.net.Uri  	setScheme android.net.Uri  toString android.net.Uri  Build 
android.os  Environment 
android.os  Handler 
android.os  Looper 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  DIRECTORY_DOCUMENTS android.os.Environment  DIRECTORY_DOWNLOADS android.os.Environment  getExternalStorageDirectory android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  post android.os.Handler  
getMainLooper android.os.Looper  Log android.util  e android.util.Log  Boolean "com.capacitorjs.plugins.filesystem  Build "com.capacitorjs.plugins.filesystem  	ByteArray "com.capacitorjs.plugins.filesystem  CoroutineScope "com.capacitorjs.plugins.filesystem  
Deprecated "com.capacitorjs.plugins.filesystem  Dispatchers "com.capacitorjs.plugins.filesystem  	DoubleUri "com.capacitorjs.plugins.filesystem  Environment "com.capacitorjs.plugins.filesystem  	ErrorInfo "com.capacitorjs.plugins.filesystem  	Exception "com.capacitorjs.plugins.filesystem  File "com.capacitorjs.plugins.filesystem  FileOutputStream "com.capacitorjs.plugins.filesystem  FilesystemErrors "com.capacitorjs.plugins.filesystem  FilesystemPlugin "com.capacitorjs.plugins.filesystem  Handler "com.capacitorjs.plugins.filesystem  HttpURLConnectionBuilder "com.capacitorjs.plugins.filesystem  INPUT_APPEND "com.capacitorjs.plugins.filesystem  INPUT_CHUNK_SIZE "com.capacitorjs.plugins.filesystem  
INPUT_DATA "com.capacitorjs.plugins.filesystem  INPUT_DIRECTORY "com.capacitorjs.plugins.filesystem  INPUT_ENCODING "com.capacitorjs.plugins.filesystem  
INPUT_FROM "com.capacitorjs.plugins.filesystem  INPUT_FROM_DIRECTORY "com.capacitorjs.plugins.filesystem  
INPUT_PATH "com.capacitorjs.plugins.filesystem  INPUT_RECURSIVE "com.capacitorjs.plugins.filesystem  INPUT_TO "com.capacitorjs.plugins.filesystem  INPUT_TO_DIRECTORY "com.capacitorjs.plugins.filesystem  IOException "com.capacitorjs.plugins.filesystem  IONFILEController "com.capacitorjs.plugins.filesystem  IONFILECreateOptions "com.capacitorjs.plugins.filesystem  IONFILEDeleteOptions "com.capacitorjs.plugins.filesystem  IONFILEEncoding "com.capacitorjs.plugins.filesystem  IONFILEFolderType "com.capacitorjs.plugins.filesystem  IONFILEReadInChunksOptions "com.capacitorjs.plugins.filesystem  IONFILEReadOptions "com.capacitorjs.plugins.filesystem  IONFILESaveMode "com.capacitorjs.plugins.filesystem  IONFILESaveOptions "com.capacitorjs.plugins.filesystem  
IONFILEUri "com.capacitorjs.plugins.filesystem  Int "com.capacitorjs.plugins.filesystem  
JSONException "com.capacitorjs.plugins.filesystem  JSObject "com.capacitorjs.plugins.filesystem  LegacyFilesystemImplementation "com.capacitorjs.plugins.filesystem  List "com.capacitorjs.plugins.filesystem  Log "com.capacitorjs.plugins.filesystem  Logger "com.capacitorjs.plugins.filesystem  Long "com.capacitorjs.plugins.filesystem  Looper "com.capacitorjs.plugins.filesystem  Manifest "com.capacitorjs.plugins.filesystem  MediaScannerConnection "com.capacitorjs.plugins.filesystem  NumberFormatException "com.capacitorjs.plugins.filesystem  OUTPUT_CREATED_TIME "com.capacitorjs.plugins.filesystem  OUTPUT_DATA "com.capacitorjs.plugins.filesystem  OUTPUT_FILES "com.capacitorjs.plugins.filesystem  OUTPUT_MODIFIED_TIME "com.capacitorjs.plugins.filesystem  OUTPUT_NAME "com.capacitorjs.plugins.filesystem  OUTPUT_SIZE "com.capacitorjs.plugins.filesystem  OUTPUT_TYPE "com.capacitorjs.plugins.filesystem  
OUTPUT_URI "com.capacitorjs.plugins.filesystem  PERMISSION_GRANTED "com.capacitorjs.plugins.filesystem  PUBLIC_STORAGE "com.capacitorjs.plugins.filesystem  PUBLIC_STORAGE_ABOVE_ANDROID_10 "com.capacitorjs.plugins.filesystem  PermissionState "com.capacitorjs.plugins.filesystem  PluginMethod "com.capacitorjs.plugins.filesystem  ProgressEmitter "com.capacitorjs.plugins.filesystem  ReadFileInChunksOptions "com.capacitorjs.plugins.filesystem  ReadFileOptions "com.capacitorjs.plugins.filesystem  SingleUriWithRecursiveOptions "com.capacitorjs.plugins.filesystem  String "com.capacitorjs.plugins.filesystem  System "com.capacitorjs.plugins.filesystem  	Throwable "com.capacitorjs.plugins.filesystem  Throws "com.capacitorjs.plugins.filesystem  URISyntaxException "com.capacitorjs.plugins.filesystem  URL "com.capacitorjs.plugins.filesystem  Unit "com.capacitorjs.plugins.filesystem  Uri "com.capacitorjs.plugins.filesystem  WriteFileOptions "com.capacitorjs.plugins.filesystem  also "com.capacitorjs.plugins.filesystem  apply "com.capacitorjs.plugins.filesystem  arrayOf "com.capacitorjs.plugins.filesystem  cancel "com.capacitorjs.plugins.filesystem  catch "com.capacitorjs.plugins.filesystem  context "com.capacitorjs.plugins.filesystem  
controller "com.capacitorjs.plugins.filesystem  createReadDirResultObject "com.capacitorjs.plugins.filesystem  createReadResultObject "com.capacitorjs.plugins.filesystem  createUriResultObject "com.capacitorjs.plugins.filesystem  createWriteResultObject "com.capacitorjs.plugins.filesystem  createdTimestamp "com.capacitorjs.plugins.filesystem  getDoubleIONFILEUri "com.capacitorjs.plugins.filesystem  getReadFileInChunksOptions "com.capacitorjs.plugins.filesystem  getReadFileOptions "com.capacitorjs.plugins.filesystem  getSingleIONFILEUri "com.capacitorjs.plugins.filesystem   getSingleUriWithRecursiveOptions "com.capacitorjs.plugins.filesystem  getValue "com.capacitorjs.plugins.filesystem  getWriteFileOptions "com.capacitorjs.plugins.filesystem  
isNotBlank "com.capacitorjs.plugins.filesystem  isStoragePermissionGranted "com.capacitorjs.plugins.filesystem  lastModifiedTimestamp "com.capacitorjs.plugins.filesystem  launch "com.capacitorjs.plugins.filesystem  launchIn "com.capacitorjs.plugins.filesystem  lazy "com.capacitorjs.plugins.filesystem  legacyImplementation "com.capacitorjs.plugins.filesystem  let "com.capacitorjs.plugins.filesystem  map "com.capacitorjs.plugins.filesystem  name "com.capacitorjs.plugins.filesystem  onCompletion "com.capacitorjs.plugins.filesystem  onEach "com.capacitorjs.plugins.filesystem  	onFailure "com.capacitorjs.plugins.filesystem  	onSuccess "com.capacitorjs.plugins.filesystem  padStart "com.capacitorjs.plugins.filesystem  
plusAssign "com.capacitorjs.plugins.filesystem  provideDelegate "com.capacitorjs.plugins.filesystem  requestAllPermissions "com.capacitorjs.plugins.filesystem  run "com.capacitorjs.plugins.filesystem  	sendError "com.capacitorjs.plugins.filesystem  sendSuccess "com.capacitorjs.plugins.filesystem  size "com.capacitorjs.plugins.filesystem  takeIf "com.capacitorjs.plugins.filesystem  thread "com.capacitorjs.plugins.filesystem  toFilesystemError "com.capacitorjs.plugins.filesystem  toInt "com.capacitorjs.plugins.filesystem  toResultObject "com.capacitorjs.plugins.filesystem  type "com.capacitorjs.plugins.filesystem  
unresolvedUri "com.capacitorjs.plugins.filesystem  	uppercase "com.capacitorjs.plugins.filesystem  uri "com.capacitorjs.plugins.filesystem  
IONFILEUri ,com.capacitorjs.plugins.filesystem.DoubleUri  fromUri ,com.capacitorjs.plugins.filesystem.DoubleUri  toUri ,com.capacitorjs.plugins.filesystem.DoubleUri  	ErrorInfo 3com.capacitorjs.plugins.filesystem.FilesystemErrors  Int 3com.capacitorjs.plugins.filesystem.FilesystemErrors  String 3com.capacitorjs.plugins.filesystem.FilesystemErrors  cannotDeleteChildren 3com.capacitorjs.plugins.filesystem.FilesystemErrors  directoryCreationAlreadyExists 3com.capacitorjs.plugins.filesystem.FilesystemErrors  doesNotExist 3com.capacitorjs.plugins.filesystem.FilesystemErrors  filePermissionsDenied 3com.capacitorjs.plugins.filesystem.FilesystemErrors  formatErrorCode 3com.capacitorjs.plugins.filesystem.FilesystemErrors  
getISNotBlank 3com.capacitorjs.plugins.filesystem.FilesystemErrors  
getIsNotBlank 3com.capacitorjs.plugins.filesystem.FilesystemErrors  getPADStart 3com.capacitorjs.plugins.filesystem.FilesystemErrors  getPadStart 3com.capacitorjs.plugins.filesystem.FilesystemErrors  invalidInputMethod 3com.capacitorjs.plugins.filesystem.FilesystemErrors  invalidPath 3com.capacitorjs.plugins.filesystem.FilesystemErrors  
isNotBlank 3com.capacitorjs.plugins.filesystem.FilesystemErrors  missingParentDirectories 3com.capacitorjs.plugins.filesystem.FilesystemErrors  
notAllowed 3com.capacitorjs.plugins.filesystem.FilesystemErrors  operationFailed 3com.capacitorjs.plugins.filesystem.FilesystemErrors  padStart 3com.capacitorjs.plugins.filesystem.FilesystemErrors  String =com.capacitorjs.plugins.filesystem.FilesystemErrors.ErrorInfo  code =com.capacitorjs.plugins.filesystem.FilesystemErrors.ErrorInfo  message =com.capacitorjs.plugins.filesystem.FilesystemErrors.ErrorInfo  Boolean 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  Build 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  CoroutineScope 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
Deprecated 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  Dispatchers 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  Environment 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	Exception 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  FilesystemErrors 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  INPUT_APPEND 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  IONFILEController 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  IONFILECreateOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  IONFILEDeleteOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
IONFILEUri 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  Int 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
JSONException 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  JSObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  LegacyFilesystemImplementation 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  Log 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  Logger 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  MediaScannerConnection 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  PERMISSION_GRANTED 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  PUBLIC_STORAGE 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  PUBLIC_STORAGE_ABOVE_ANDROID_10 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  PermissionCallback 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  PermissionState 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
PluginCall 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  PluginMethod 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  ProgressEmitter 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  ReadFileInChunksOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  ReadFileOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  Unit 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  WriteFileOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  also 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  arrayOf 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  bridge 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  cancel 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  catch 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  context 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
controller 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  copy 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  coroutineScope 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  createReadDirResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  createReadResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  createUriResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  createWriteResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
deleteFile 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  downloadFile 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getALSO 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
getARRAYOf 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getAlso 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
getArrayOf 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	getCANCEL 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCATCH 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
getCONTEXT 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCREATEReadDirResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCREATEReadResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCREATEUriResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCREATEWriteResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	getCancel 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCatch 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
getContext 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCreateReadDirResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCreateReadResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCreateUriResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getCreateWriteResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getDoubleIONFILEUri 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGETDoubleIONFILEUri 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGETReadFileInChunksOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGETReadFileOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGETSingleIONFILEUri 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  #getGETSingleUriWithRecursiveOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGETValue 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGETWriteFileOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGetDoubleIONFILEUri 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGetReadFileInChunksOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGetReadFileOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGetSingleIONFILEUri 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  #getGetSingleUriWithRecursiveOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGetValue 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getGetWriteFileOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	getLAUNCH 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getLAUNCHIn 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getLAZY 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getLET 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	getLOGTag 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	getLaunch 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getLaunchIn 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getLazy 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getLet 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	getLogTag 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getONCompletion 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	getONEach 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getONFailure 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getONSuccess 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getOnCompletion 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	getOnEach 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getOnFailure 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getOnSuccess 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getPROVIDEDelegate 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getPermissionState 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getProvideDelegate 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getRUN 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getReadFileInChunksOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getReadFileOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getRun 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getSENDError 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getSENDSuccess 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getSendError 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getSendSuccess 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getSingleIONFILEUri 3com.capacitorjs.plugins.filesystem.FilesystemPlugin   getSingleUriWithRecursiveOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getTOFilesystemError 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getTOResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getToFilesystemError 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getToResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getUri 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getValue 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  getWriteFileOptions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  isStoragePermissionGranted 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  launch 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  launchIn 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  lazy 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  legacyImplementation 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  let 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  logTag 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  mkdir 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  notifyListeners 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  onCompletion 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  onEach 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	onFailure 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	onSuccess 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  permissionCallback 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  provideDelegate 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  readFile 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  readFileInChunks 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  readdir 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  rename 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  requestAllPermissions 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  rmdir 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  run 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  runWithPermission 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	sendError 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  sendSuccess 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
setContext 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	setLogTag 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  stat 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  toFilesystemError 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  toResultObject 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  	writeFile 3com.capacitorjs.plugins.filesystem.FilesystemPlugin  
getARRAYOf Scom.capacitorjs.plugins.filesystem.FilesystemPlugin.downloadFile.<no name provided>  
getArrayOf Scom.capacitorjs.plugins.filesystem.FilesystemPlugin.downloadFile.<no name provided>  
getCONTEXT Scom.capacitorjs.plugins.filesystem.FilesystemPlugin.downloadFile.<no name provided>  
getContext Scom.capacitorjs.plugins.filesystem.FilesystemPlugin.downloadFile.<no name provided>  getLEGACYImplementation Scom.capacitorjs.plugins.filesystem.FilesystemPlugin.downloadFile.<no name provided>  getLegacyImplementation Scom.capacitorjs.plugins.filesystem.FilesystemPlugin.downloadFile.<no name provided>  Boolean Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Bridge Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  	ByteArray Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Context Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Environment Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  	Exception Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  File Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  FileOutputStream Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  FilesystemDownloadCallback Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Handler Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  HttpURLConnectionBuilder Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  IOException Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Int Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  
JSONException Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  JSObject Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Long Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Looper Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  NumberFormatException Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  
PluginCall Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  ProgressEmitter Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  String Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  System Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Throws Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  URISyntaxException Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  URL Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  Uri Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  also Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  context Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  doDownloadInBackground Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  downloadFile Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  getALSO Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  getAlso Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  getDirectory Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  
getFileObject Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  
getPLUSAssign Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  
getPlusAssign Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  	getTHREAD Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  getTOInt Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  	getThread Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  getToInt Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  getUPPERCASE Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  getUppercase Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  isPublicDirectory Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  
plusAssign Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  thread Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  toInt Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  	uppercase Acom.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation  	Exception \com.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation.FilesystemDownloadCallback  JSObject \com.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation.FilesystemDownloadCallback  onError \com.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation.FilesystemDownloadCallback  	onSuccess \com.capacitorjs.plugins.filesystem.LegacyFilesystemImplementation.FilesystemDownloadCallback  IONFILEReadInChunksOptions :com.capacitorjs.plugins.filesystem.ReadFileInChunksOptions  
IONFILEUri :com.capacitorjs.plugins.filesystem.ReadFileInChunksOptions  options :com.capacitorjs.plugins.filesystem.ReadFileInChunksOptions  uri :com.capacitorjs.plugins.filesystem.ReadFileInChunksOptions  IONFILEReadOptions 2com.capacitorjs.plugins.filesystem.ReadFileOptions  
IONFILEUri 2com.capacitorjs.plugins.filesystem.ReadFileOptions  options 2com.capacitorjs.plugins.filesystem.ReadFileOptions  uri 2com.capacitorjs.plugins.filesystem.ReadFileOptions  Boolean @com.capacitorjs.plugins.filesystem.SingleUriWithRecursiveOptions  
IONFILEUri @com.capacitorjs.plugins.filesystem.SingleUriWithRecursiveOptions  	recursive @com.capacitorjs.plugins.filesystem.SingleUriWithRecursiveOptions  uri @com.capacitorjs.plugins.filesystem.SingleUriWithRecursiveOptions  IONFILESaveOptions 3com.capacitorjs.plugins.filesystem.WriteFileOptions  
IONFILEUri 3com.capacitorjs.plugins.filesystem.WriteFileOptions  options 3com.capacitorjs.plugins.filesystem.WriteFileOptions  uri 3com.capacitorjs.plugins.filesystem.WriteFileOptions  Bridge com.getcapacitor  JSArray com.getcapacitor  JSObject com.getcapacitor  Logger com.getcapacitor  PermissionState com.getcapacitor  Plugin com.getcapacitor  
PluginCall com.getcapacitor  PluginMethod com.getcapacitor  OUTPUT_CREATED_TIME com.getcapacitor.JSObject  OUTPUT_MODIFIED_TIME com.getcapacitor.JSObject  OUTPUT_NAME com.getcapacitor.JSObject  OUTPUT_SIZE com.getcapacitor.JSObject  OUTPUT_TYPE com.getcapacitor.JSObject  
OUTPUT_URI com.getcapacitor.JSObject  also com.getcapacitor.JSObject  apply com.getcapacitor.JSObject  createdTimestamp com.getcapacitor.JSObject  equals com.getcapacitor.JSObject  getALSO com.getcapacitor.JSObject  getAPPLY com.getcapacitor.JSObject  getAlso com.getcapacitor.JSObject  getApply com.getcapacitor.JSObject  getCREATEDTimestamp com.getcapacitor.JSObject  getCreatedTimestamp com.getcapacitor.JSObject  getLASTModifiedTimestamp com.getcapacitor.JSObject  getLastModifiedTimestamp com.getcapacitor.JSObject  getNAME com.getcapacitor.JSObject  getName com.getcapacitor.JSObject  getSIZE com.getcapacitor.JSObject  getSize com.getcapacitor.JSObject  	getString com.getcapacitor.JSObject  getTYPE com.getcapacitor.JSObject  getType com.getcapacitor.JSObject  getURI com.getcapacitor.JSObject  getUri com.getcapacitor.JSObject  lastModifiedTimestamp com.getcapacitor.JSObject  name com.getcapacitor.JSObject  put com.getcapacitor.JSObject  putOpt com.getcapacitor.JSObject  size com.getcapacitor.JSObject  type com.getcapacitor.JSObject  uri com.getcapacitor.JSObject  debug com.getcapacitor.Logger  GRANTED  com.getcapacitor.PermissionState  equals  com.getcapacitor.PermissionState  Boolean com.getcapacitor.Plugin  Build com.getcapacitor.Plugin  CoroutineScope com.getcapacitor.Plugin  
Deprecated com.getcapacitor.Plugin  Dispatchers com.getcapacitor.Plugin  Environment com.getcapacitor.Plugin  	Exception com.getcapacitor.Plugin  FilesystemErrors com.getcapacitor.Plugin  INPUT_APPEND com.getcapacitor.Plugin  IONFILEController com.getcapacitor.Plugin  IONFILECreateOptions com.getcapacitor.Plugin  IONFILEDeleteOptions com.getcapacitor.Plugin  
IONFILEUri com.getcapacitor.Plugin  Int com.getcapacitor.Plugin  
JSONException com.getcapacitor.Plugin  JSObject com.getcapacitor.Plugin  LegacyFilesystemImplementation com.getcapacitor.Plugin  Log com.getcapacitor.Plugin  Logger com.getcapacitor.Plugin  MediaScannerConnection com.getcapacitor.Plugin  PERMISSION_GRANTED com.getcapacitor.Plugin  PUBLIC_STORAGE com.getcapacitor.Plugin  PUBLIC_STORAGE_ABOVE_ANDROID_10 com.getcapacitor.Plugin  PermissionCallback com.getcapacitor.Plugin  PermissionState com.getcapacitor.Plugin  
PluginCall com.getcapacitor.Plugin  PluginMethod com.getcapacitor.Plugin  ProgressEmitter com.getcapacitor.Plugin  ReadFileInChunksOptions com.getcapacitor.Plugin  ReadFileOptions com.getcapacitor.Plugin  Unit com.getcapacitor.Plugin  WriteFileOptions com.getcapacitor.Plugin  also com.getcapacitor.Plugin  arrayOf com.getcapacitor.Plugin  cancel com.getcapacitor.Plugin  catch com.getcapacitor.Plugin  checkPermissions com.getcapacitor.Plugin  context com.getcapacitor.Plugin  
controller com.getcapacitor.Plugin  copy com.getcapacitor.Plugin  createReadDirResultObject com.getcapacitor.Plugin  createReadResultObject com.getcapacitor.Plugin  createUriResultObject com.getcapacitor.Plugin  createWriteResultObject com.getcapacitor.Plugin  
deleteFile com.getcapacitor.Plugin  downloadFile com.getcapacitor.Plugin  getDoubleIONFILEUri com.getcapacitor.Plugin  getPermissionState com.getcapacitor.Plugin  getReadFileInChunksOptions com.getcapacitor.Plugin  getReadFileOptions com.getcapacitor.Plugin  getSingleIONFILEUri com.getcapacitor.Plugin   getSingleUriWithRecursiveOptions com.getcapacitor.Plugin  getUri com.getcapacitor.Plugin  getValue com.getcapacitor.Plugin  getWriteFileOptions com.getcapacitor.Plugin  handleOnDestroy com.getcapacitor.Plugin  isStoragePermissionGranted com.getcapacitor.Plugin  launch com.getcapacitor.Plugin  launchIn com.getcapacitor.Plugin  lazy com.getcapacitor.Plugin  legacyImplementation com.getcapacitor.Plugin  let com.getcapacitor.Plugin  load com.getcapacitor.Plugin  mkdir com.getcapacitor.Plugin  notifyListeners com.getcapacitor.Plugin  onCompletion com.getcapacitor.Plugin  onEach com.getcapacitor.Plugin  	onFailure com.getcapacitor.Plugin  	onSuccess com.getcapacitor.Plugin  permissionCallback com.getcapacitor.Plugin  provideDelegate com.getcapacitor.Plugin  readFile com.getcapacitor.Plugin  readFileInChunks com.getcapacitor.Plugin  readdir com.getcapacitor.Plugin  rename com.getcapacitor.Plugin  requestAllPermissions com.getcapacitor.Plugin  requestPermissions com.getcapacitor.Plugin  rmdir com.getcapacitor.Plugin  run com.getcapacitor.Plugin  runWithPermission com.getcapacitor.Plugin  	sendError com.getcapacitor.Plugin  sendSuccess com.getcapacitor.Plugin  stat com.getcapacitor.Plugin  toFilesystemError com.getcapacitor.Plugin  toResultObject com.getcapacitor.Plugin  	writeFile com.getcapacitor.Plugin  	DoubleUri com.getcapacitor.PluginCall  INPUT_APPEND com.getcapacitor.PluginCall  INPUT_CHUNK_SIZE com.getcapacitor.PluginCall  
INPUT_DATA com.getcapacitor.PluginCall  INPUT_DIRECTORY com.getcapacitor.PluginCall  INPUT_ENCODING com.getcapacitor.PluginCall  
INPUT_FROM com.getcapacitor.PluginCall  INPUT_FROM_DIRECTORY com.getcapacitor.PluginCall  
INPUT_PATH com.getcapacitor.PluginCall  INPUT_RECURSIVE com.getcapacitor.PluginCall  INPUT_TO com.getcapacitor.PluginCall  INPUT_TO_DIRECTORY com.getcapacitor.PluginCall  IONFILEEncoding com.getcapacitor.PluginCall  IONFILEFolderType com.getcapacitor.PluginCall  IONFILEReadInChunksOptions com.getcapacitor.PluginCall  IONFILEReadOptions com.getcapacitor.PluginCall  IONFILESaveMode com.getcapacitor.PluginCall  IONFILESaveOptions com.getcapacitor.PluginCall  
IONFILEUri com.getcapacitor.PluginCall  ReadFileInChunksOptions com.getcapacitor.PluginCall  ReadFileOptions com.getcapacitor.PluginCall  SingleUriWithRecursiveOptions com.getcapacitor.PluginCall  WriteFileOptions com.getcapacitor.PluginCall  data com.getcapacitor.PluginCall  
getBoolean com.getcapacitor.PluginCall  getDATA com.getcapacitor.PluginCall  getData com.getcapacitor.PluginCall  getDoubleIONFILEUri com.getcapacitor.PluginCall  getGETDoubleIONFILEUri com.getcapacitor.PluginCall  getGETReadFileInChunksOptions com.getcapacitor.PluginCall  getGETReadFileOptions com.getcapacitor.PluginCall  getGETSingleIONFILEUri com.getcapacitor.PluginCall  #getGETSingleUriWithRecursiveOptions com.getcapacitor.PluginCall  getGETWriteFileOptions com.getcapacitor.PluginCall  getGetDoubleIONFILEUri com.getcapacitor.PluginCall  getGetReadFileInChunksOptions com.getcapacitor.PluginCall  getGetReadFileOptions com.getcapacitor.PluginCall  getGetSingleIONFILEUri com.getcapacitor.PluginCall  #getGetSingleUriWithRecursiveOptions com.getcapacitor.PluginCall  getGetWriteFileOptions com.getcapacitor.PluginCall  getInt com.getcapacitor.PluginCall  getLET com.getcapacitor.PluginCall  getLet com.getcapacitor.PluginCall  
getMETHODName com.getcapacitor.PluginCall  
getMethodName com.getcapacitor.PluginCall  	getObject com.getcapacitor.PluginCall  getReadFileInChunksOptions com.getcapacitor.PluginCall  getReadFileOptions com.getcapacitor.PluginCall  getSENDError com.getcapacitor.PluginCall  getSENDSuccess com.getcapacitor.PluginCall  getSendError com.getcapacitor.PluginCall  getSendSuccess com.getcapacitor.PluginCall  getSingleIONFILEUri com.getcapacitor.PluginCall   getSingleUriWithRecursiveOptions com.getcapacitor.PluginCall  	getString com.getcapacitor.PluginCall  	getTAKEIf com.getcapacitor.PluginCall  	getTakeIf com.getcapacitor.PluginCall  getUNRESOLVEDUri com.getcapacitor.PluginCall  getUnresolvedUri com.getcapacitor.PluginCall  getWriteFileOptions com.getcapacitor.PluginCall  let com.getcapacitor.PluginCall  
methodName com.getcapacitor.PluginCall  reject com.getcapacitor.PluginCall  resolve com.getcapacitor.PluginCall  	sendError com.getcapacitor.PluginCall  sendSuccess com.getcapacitor.PluginCall  setData com.getcapacitor.PluginCall  setKeepAlive com.getcapacitor.PluginCall  
setMethodName com.getcapacitor.PluginCall  takeIf com.getcapacitor.PluginCall  
unresolvedUri com.getcapacitor.PluginCall  RETURN_CALLBACK com.getcapacitor.PluginMethod  CapacitorPlugin com.getcapacitor.annotation  
Permission com.getcapacitor.annotation  PermissionCallback com.getcapacitor.annotation  CapacitorHttpUrlConnection com.getcapacitor.plugin.util  HttpRequestHandler com.getcapacitor.plugin.util  getHeaderField 7com.getcapacitor.plugin.util.CapacitorHttpUrlConnection  getINPUTStream 7com.getcapacitor.plugin.util.CapacitorHttpUrlConnection  getInputStream 7com.getcapacitor.plugin.util.CapacitorHttpUrlConnection  inputStream 7com.getcapacitor.plugin.util.CapacitorHttpUrlConnection  setInputStream 7com.getcapacitor.plugin.util.CapacitorHttpUrlConnection  setSSLSocketFactory 7com.getcapacitor.plugin.util.CapacitorHttpUrlConnection  HttpURLConnectionBuilder /com.getcapacitor.plugin.util.HttpRequestHandler  ProgressEmitter /com.getcapacitor.plugin.util.HttpRequestHandler  build Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  openConnection Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  setConnectTimeout Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  setDisableRedirects Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  
setHeaders Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  	setMethod Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  setReadTimeout Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  setUrl Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  setUrlParams Hcom.getcapacitor.plugin.util.HttpRequestHandler.HttpURLConnectionBuilder  <SAM-CONSTRUCTOR> ?com.getcapacitor.plugin.util.HttpRequestHandler.ProgressEmitter  emit ?com.getcapacitor.plugin.util.HttpRequestHandler.ProgressEmitter  IONFILEController io.ionic.libs.ionfilesystemlib  copy 0io.ionic.libs.ionfilesystemlib.IONFILEController  createDirectory 0io.ionic.libs.ionfilesystemlib.IONFILEController  delete 0io.ionic.libs.ionfilesystemlib.IONFILEController  
getFileUri 0io.ionic.libs.ionfilesystemlib.IONFILEController  getMetadata 0io.ionic.libs.ionfilesystemlib.IONFILEController  
listDirectory 0io.ionic.libs.ionfilesystemlib.IONFILEController  move 0io.ionic.libs.ionfilesystemlib.IONFILEController  readFile 0io.ionic.libs.ionfilesystemlib.IONFILEController  readFileInChunks 0io.ionic.libs.ionfilesystemlib.IONFILEController  saveFile 0io.ionic.libs.ionfilesystemlib.IONFILEController  IONFILECreateOptions $io.ionic.libs.ionfilesystemlib.model  IONFILEDeleteOptions $io.ionic.libs.ionfilesystemlib.model  IONFILEEncoding $io.ionic.libs.ionfilesystemlib.model  IONFILEExceptions $io.ionic.libs.ionfilesystemlib.model  IONFILEFileType $io.ionic.libs.ionfilesystemlib.model  IONFILEFolderType $io.ionic.libs.ionfilesystemlib.model  IONFILEMetadataResult $io.ionic.libs.ionfilesystemlib.model  IONFILEReadInChunksOptions $io.ionic.libs.ionfilesystemlib.model  IONFILEReadOptions $io.ionic.libs.ionfilesystemlib.model  IONFILESaveMode $io.ionic.libs.ionfilesystemlib.model  IONFILESaveOptions $io.ionic.libs.ionfilesystemlib.model  
IONFILEUri $io.ionic.libs.ionfilesystemlib.model  fromEncodingName 4io.ionic.libs.ionfilesystemlib.model.IONFILEEncoding  fromEncodingName >io.ionic.libs.ionfilesystemlib.model.IONFILEEncoding.Companion  CopyRenameFailed 6io.ionic.libs.ionfilesystemlib.model.IONFILEExceptions  CreateFailed 6io.ionic.libs.ionfilesystemlib.model.IONFILEExceptions  DeleteFailed 6io.ionic.libs.ionfilesystemlib.model.IONFILEExceptions  DoesNotExist 6io.ionic.libs.ionfilesystemlib.model.IONFILEExceptions  NotSupportedForContentScheme 6io.ionic.libs.ionfilesystemlib.model.IONFILEExceptions  NotSupportedForDirectory 6io.ionic.libs.ionfilesystemlib.model.IONFILEExceptions  NotSupportedForFiles 6io.ionic.libs.ionfilesystemlib.model.IONFILEExceptions  UnresolvableUri 6io.ionic.libs.ionfilesystemlib.model.IONFILEExceptions  DestinationDirectoryExists Gio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CopyRenameFailed  LocalToContent Gio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CopyRenameFailed  MixingFilesAndDirectories Gio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CopyRenameFailed  NoParentDirectory Gio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CopyRenameFailed  SourceAndDestinationContent Gio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CopyRenameFailed  FilesystemErrors bio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CopyRenameFailed.DestinationDirectoryExists  path bio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CopyRenameFailed.DestinationDirectoryExists  FilesystemErrors Yio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CopyRenameFailed.NoParentDirectory  
AlreadyExists Cio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CreateFailed  NoParentDirectory Cio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CreateFailed  FilesystemErrors Qio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CreateFailed.AlreadyExists  path Qio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CreateFailed.AlreadyExists  FilesystemErrors Uio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.CreateFailed.NoParentDirectory  CannotDeleteChildren Cio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.DeleteFailed  FilesystemErrors Xio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.DeleteFailed.CannotDeleteChildren  FilesystemErrors Cio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.DoesNotExist  path Cio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.DoesNotExist  FilesystemErrors Sio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.NotSupportedForContentScheme  FilesystemErrors Oio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.NotSupportedForDirectory  FilesystemErrors Kio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.NotSupportedForFiles  FilesystemErrors Fio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.UnresolvableUri  uri Fio.ionic.libs.ionfilesystemlib.model.IONFILEExceptions.UnresolvableUri  	Directory 4io.ionic.libs.ionfilesystemlib.model.IONFILEFileType  equals 6io.ionic.libs.ionfilesystemlib.model.IONFILEFolderType  fromStringAlias 6io.ionic.libs.ionfilesystemlib.model.IONFILEFolderType  fromStringAlias @io.ionic.libs.ionfilesystemlib.model.IONFILEFolderType.Companion  JSObject :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  OUTPUT_CREATED_TIME :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  OUTPUT_MODIFIED_TIME :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  OUTPUT_NAME :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  OUTPUT_SIZE :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  OUTPUT_TYPE :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  
OUTPUT_URI :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  apply :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  createdTimestamp :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  getAPPLY :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  getApply :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  getTOResultObject :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  getToResultObject :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  lastModifiedTimestamp :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  name :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  size :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  toResultObject :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  type :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  uri :io.ionic.libs.ionfilesystemlib.model.IONFILEMetadataResult  APPEND 4io.ionic.libs.ionfilesystemlib.model.IONFILESaveMode  WRITE 4io.ionic.libs.ionfilesystemlib.model.IONFILESaveMode  equals 4io.ionic.libs.ionfilesystemlib.model.IONFILESaveMode  mode 7io.ionic.libs.ionfilesystemlib.model.IONFILESaveOptions  Resolved /io.ionic.libs.ionfilesystemlib.model.IONFILEUri  
Unresolved /io.ionic.libs.ionfilesystemlib.model.IONFILEUri  toResultObject /io.ionic.libs.ionfilesystemlib.model.IONFILEUri  createUriResultObject 8io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Resolved  getCREATEUriResultObject 8io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Resolved  getCreateUriResultObject 8io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Resolved  getTOResultObject 8io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Resolved  getToResultObject 8io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Resolved  inExternalStorage 8io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Resolved  toResultObject 8io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Resolved  uri 8io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Resolved  parentFolder :io.ionic.libs.ionfilesystemlib.model.IONFILEUri.Unresolved  File java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  absolutePath java.io.File  equals java.io.File  exists java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  mkdir java.io.File  setAbsolutePath java.io.File  close java.io.FileOutputStream  write java.io.FileOutputStream  close java.io.InputStream  read java.io.InputStream  close java.io.OutputStream  write java.io.OutputStream  Build 	java.lang  	ByteArray 	java.lang  CoroutineScope 	java.lang  Dispatchers 	java.lang  	DoubleUri 	java.lang  Environment 	java.lang  	ErrorInfo 	java.lang  	Exception 	java.lang  File 	java.lang  FileOutputStream 	java.lang  FilesystemErrors 	java.lang  Handler 	java.lang  HttpURLConnectionBuilder 	java.lang  INPUT_APPEND 	java.lang  INPUT_CHUNK_SIZE 	java.lang  
INPUT_DATA 	java.lang  INPUT_DIRECTORY 	java.lang  INPUT_ENCODING 	java.lang  
INPUT_FROM 	java.lang  INPUT_FROM_DIRECTORY 	java.lang  
INPUT_PATH 	java.lang  INPUT_RECURSIVE 	java.lang  INPUT_TO 	java.lang  INPUT_TO_DIRECTORY 	java.lang  IOException 	java.lang  IONFILEController 	java.lang  IONFILECreateOptions 	java.lang  IONFILEDeleteOptions 	java.lang  IONFILEEncoding 	java.lang  IONFILEFolderType 	java.lang  IONFILEReadInChunksOptions 	java.lang  IONFILEReadOptions 	java.lang  IONFILESaveMode 	java.lang  IONFILESaveOptions 	java.lang  
IONFILEUri 	java.lang  
JSONException 	java.lang  JSObject 	java.lang  LegacyFilesystemImplementation 	java.lang  Log 	java.lang  Logger 	java.lang  Looper 	java.lang  Manifest 	java.lang  MediaScannerConnection 	java.lang  OUTPUT_CREATED_TIME 	java.lang  OUTPUT_MODIFIED_TIME 	java.lang  OUTPUT_NAME 	java.lang  OUTPUT_SIZE 	java.lang  OUTPUT_TYPE 	java.lang  
OUTPUT_URI 	java.lang  PERMISSION_GRANTED 	java.lang  PUBLIC_STORAGE 	java.lang  PUBLIC_STORAGE_ABOVE_ANDROID_10 	java.lang  PermissionState 	java.lang  PluginMethod 	java.lang  ProgressEmitter 	java.lang  ReadFileInChunksOptions 	java.lang  ReadFileOptions 	java.lang  SingleUriWithRecursiveOptions 	java.lang  System 	java.lang  Thread 	java.lang  URISyntaxException 	java.lang  URL 	java.lang  Uri 	java.lang  WriteFileOptions 	java.lang  also 	java.lang  apply 	java.lang  arrayOf 	java.lang  cancel 	java.lang  catch 	java.lang  context 	java.lang  
controller 	java.lang  createReadDirResultObject 	java.lang  createReadResultObject 	java.lang  createUriResultObject 	java.lang  createWriteResultObject 	java.lang  createdTimestamp 	java.lang  getDoubleIONFILEUri 	java.lang  getReadFileInChunksOptions 	java.lang  getReadFileOptions 	java.lang  getSingleIONFILEUri 	java.lang   getSingleUriWithRecursiveOptions 	java.lang  getValue 	java.lang  getWriteFileOptions 	java.lang  
isNotBlank 	java.lang  isStoragePermissionGranted 	java.lang  lastModifiedTimestamp 	java.lang  launch 	java.lang  launchIn 	java.lang  lazy 	java.lang  legacyImplementation 	java.lang  let 	java.lang  map 	java.lang  name 	java.lang  onCompletion 	java.lang  onEach 	java.lang  	onFailure 	java.lang  	onSuccess 	java.lang  padStart 	java.lang  
plusAssign 	java.lang  provideDelegate 	java.lang  requestAllPermissions 	java.lang  run 	java.lang  	sendError 	java.lang  sendSuccess 	java.lang  size 	java.lang  takeIf 	java.lang  thread 	java.lang  toFilesystemError 	java.lang  toInt 	java.lang  toResultObject 	java.lang  type 	java.lang  
unresolvedUri 	java.lang  	uppercase 	java.lang  uri 	java.lang  getLOCALIZEDMessage java.lang.Exception  getLocalizedMessage java.lang.Exception  localizedMessage java.lang.Exception  setLocalizedMessage java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  URISyntaxException java.net  URL java.net  Array kotlin  Boolean kotlin  Build kotlin  	ByteArray kotlin  Char kotlin  CoroutineScope kotlin  
Deprecated kotlin  Dispatchers kotlin  	DoubleUri kotlin  Environment kotlin  	ErrorInfo kotlin  	Exception kotlin  File kotlin  FileOutputStream kotlin  FilesystemErrors kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Handler kotlin  HttpURLConnectionBuilder kotlin  INPUT_APPEND kotlin  INPUT_CHUNK_SIZE kotlin  
INPUT_DATA kotlin  INPUT_DIRECTORY kotlin  INPUT_ENCODING kotlin  
INPUT_FROM kotlin  INPUT_FROM_DIRECTORY kotlin  
INPUT_PATH kotlin  INPUT_RECURSIVE kotlin  INPUT_TO kotlin  INPUT_TO_DIRECTORY kotlin  IOException kotlin  IONFILEController kotlin  IONFILECreateOptions kotlin  IONFILEDeleteOptions kotlin  IONFILEEncoding kotlin  IONFILEFolderType kotlin  IONFILEReadInChunksOptions kotlin  IONFILEReadOptions kotlin  IONFILESaveMode kotlin  IONFILESaveOptions kotlin  
IONFILEUri kotlin  Int kotlin  
JSONException kotlin  JSObject kotlin  Lazy kotlin  LegacyFilesystemImplementation kotlin  Log kotlin  Logger kotlin  Long kotlin  Looper kotlin  Manifest kotlin  MediaScannerConnection kotlin  Nothing kotlin  NumberFormatException kotlin  OUTPUT_CREATED_TIME kotlin  OUTPUT_MODIFIED_TIME kotlin  OUTPUT_NAME kotlin  OUTPUT_SIZE kotlin  OUTPUT_TYPE kotlin  
OUTPUT_URI kotlin  PERMISSION_GRANTED kotlin  PUBLIC_STORAGE kotlin  PUBLIC_STORAGE_ABOVE_ANDROID_10 kotlin  PermissionState kotlin  PluginMethod kotlin  ProgressEmitter kotlin  ReadFileInChunksOptions kotlin  ReadFileOptions kotlin  Result kotlin  SingleUriWithRecursiveOptions kotlin  String kotlin  System kotlin  	Throwable kotlin  Throws kotlin  URISyntaxException kotlin  URL kotlin  Unit kotlin  Uri kotlin  WriteFileOptions kotlin  also kotlin  apply kotlin  arrayOf kotlin  cancel kotlin  catch kotlin  context kotlin  
controller kotlin  createReadDirResultObject kotlin  createReadResultObject kotlin  createUriResultObject kotlin  createWriteResultObject kotlin  createdTimestamp kotlin  getDoubleIONFILEUri kotlin  getReadFileInChunksOptions kotlin  getReadFileOptions kotlin  getSingleIONFILEUri kotlin   getSingleUriWithRecursiveOptions kotlin  getValue kotlin  getWriteFileOptions kotlin  
isNotBlank kotlin  isStoragePermissionGranted kotlin  lastModifiedTimestamp kotlin  launch kotlin  launchIn kotlin  lazy kotlin  legacyImplementation kotlin  let kotlin  map kotlin  name kotlin  onCompletion kotlin  onEach kotlin  	onFailure kotlin  	onSuccess kotlin  padStart kotlin  
plusAssign kotlin  provideDelegate kotlin  requestAllPermissions kotlin  run kotlin  	sendError kotlin  sendSuccess kotlin  size kotlin  takeIf kotlin  thread kotlin  toFilesystemError kotlin  toInt kotlin  toResultObject kotlin  type kotlin  
unresolvedUri kotlin  	uppercase kotlin  uri kotlin  getALSO 
kotlin.Int  getAlso 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  	getTAKEIf 
kotlin.Int  	getTakeIf 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getONFailure 
kotlin.Result  getONSuccess 
kotlin.Result  getOnFailure 
kotlin.Result  getOnSuccess 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  
getISNotBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  getPADStart 
kotlin.String  getPadStart 
kotlin.String  getTOInt 
kotlin.String  getToInt 
kotlin.String  getUPPERCASE 
kotlin.String  getUppercase 
kotlin.String  
isNotBlank 
kotlin.String  getLOCALIZEDMessage kotlin.Throwable  getLocalizedMessage kotlin.Throwable  getTOFilesystemError kotlin.Throwable  getToFilesystemError kotlin.Throwable  setLocalizedMessage kotlin.Throwable  Build kotlin.annotation  	ByteArray kotlin.annotation  CoroutineScope kotlin.annotation  Dispatchers kotlin.annotation  	DoubleUri kotlin.annotation  Environment kotlin.annotation  	ErrorInfo kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  FileOutputStream kotlin.annotation  FilesystemErrors kotlin.annotation  Handler kotlin.annotation  HttpURLConnectionBuilder kotlin.annotation  INPUT_APPEND kotlin.annotation  INPUT_CHUNK_SIZE kotlin.annotation  
INPUT_DATA kotlin.annotation  INPUT_DIRECTORY kotlin.annotation  INPUT_ENCODING kotlin.annotation  
INPUT_FROM kotlin.annotation  INPUT_FROM_DIRECTORY kotlin.annotation  
INPUT_PATH kotlin.annotation  INPUT_RECURSIVE kotlin.annotation  INPUT_TO kotlin.annotation  INPUT_TO_DIRECTORY kotlin.annotation  IOException kotlin.annotation  IONFILEController kotlin.annotation  IONFILECreateOptions kotlin.annotation  IONFILEDeleteOptions kotlin.annotation  IONFILEEncoding kotlin.annotation  IONFILEFolderType kotlin.annotation  IONFILEReadInChunksOptions kotlin.annotation  IONFILEReadOptions kotlin.annotation  IONFILESaveMode kotlin.annotation  IONFILESaveOptions kotlin.annotation  
IONFILEUri kotlin.annotation  
JSONException kotlin.annotation  JSObject kotlin.annotation  LegacyFilesystemImplementation kotlin.annotation  Log kotlin.annotation  Logger kotlin.annotation  Looper kotlin.annotation  Manifest kotlin.annotation  MediaScannerConnection kotlin.annotation  NumberFormatException kotlin.annotation  OUTPUT_CREATED_TIME kotlin.annotation  OUTPUT_MODIFIED_TIME kotlin.annotation  OUTPUT_NAME kotlin.annotation  OUTPUT_SIZE kotlin.annotation  OUTPUT_TYPE kotlin.annotation  
OUTPUT_URI kotlin.annotation  PERMISSION_GRANTED kotlin.annotation  PUBLIC_STORAGE kotlin.annotation  PUBLIC_STORAGE_ABOVE_ANDROID_10 kotlin.annotation  PermissionState kotlin.annotation  PluginMethod kotlin.annotation  ProgressEmitter kotlin.annotation  ReadFileInChunksOptions kotlin.annotation  ReadFileOptions kotlin.annotation  SingleUriWithRecursiveOptions kotlin.annotation  System kotlin.annotation  Throws kotlin.annotation  URISyntaxException kotlin.annotation  URL kotlin.annotation  Uri kotlin.annotation  WriteFileOptions kotlin.annotation  also kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  cancel kotlin.annotation  catch kotlin.annotation  context kotlin.annotation  
controller kotlin.annotation  createReadDirResultObject kotlin.annotation  createReadResultObject kotlin.annotation  createUriResultObject kotlin.annotation  createWriteResultObject kotlin.annotation  createdTimestamp kotlin.annotation  getDoubleIONFILEUri kotlin.annotation  getReadFileInChunksOptions kotlin.annotation  getReadFileOptions kotlin.annotation  getSingleIONFILEUri kotlin.annotation   getSingleUriWithRecursiveOptions kotlin.annotation  getValue kotlin.annotation  getWriteFileOptions kotlin.annotation  
isNotBlank kotlin.annotation  isStoragePermissionGranted kotlin.annotation  lastModifiedTimestamp kotlin.annotation  launch kotlin.annotation  launchIn kotlin.annotation  lazy kotlin.annotation  legacyImplementation kotlin.annotation  let kotlin.annotation  map kotlin.annotation  name kotlin.annotation  onCompletion kotlin.annotation  onEach kotlin.annotation  	onFailure kotlin.annotation  	onSuccess kotlin.annotation  padStart kotlin.annotation  
plusAssign kotlin.annotation  provideDelegate kotlin.annotation  requestAllPermissions kotlin.annotation  run kotlin.annotation  	sendError kotlin.annotation  sendSuccess kotlin.annotation  size kotlin.annotation  takeIf kotlin.annotation  thread kotlin.annotation  toFilesystemError kotlin.annotation  toInt kotlin.annotation  toResultObject kotlin.annotation  type kotlin.annotation  
unresolvedUri kotlin.annotation  	uppercase kotlin.annotation  uri kotlin.annotation  Build kotlin.collections  	ByteArray kotlin.collections  CoroutineScope kotlin.collections  Dispatchers kotlin.collections  	DoubleUri kotlin.collections  Environment kotlin.collections  	ErrorInfo kotlin.collections  	Exception kotlin.collections  File kotlin.collections  FileOutputStream kotlin.collections  FilesystemErrors kotlin.collections  Handler kotlin.collections  HttpURLConnectionBuilder kotlin.collections  INPUT_APPEND kotlin.collections  INPUT_CHUNK_SIZE kotlin.collections  
INPUT_DATA kotlin.collections  INPUT_DIRECTORY kotlin.collections  INPUT_ENCODING kotlin.collections  
INPUT_FROM kotlin.collections  INPUT_FROM_DIRECTORY kotlin.collections  
INPUT_PATH kotlin.collections  INPUT_RECURSIVE kotlin.collections  INPUT_TO kotlin.collections  INPUT_TO_DIRECTORY kotlin.collections  IOException kotlin.collections  IONFILEController kotlin.collections  IONFILECreateOptions kotlin.collections  IONFILEDeleteOptions kotlin.collections  IONFILEEncoding kotlin.collections  IONFILEFolderType kotlin.collections  IONFILEReadInChunksOptions kotlin.collections  IONFILEReadOptions kotlin.collections  IONFILESaveMode kotlin.collections  IONFILESaveOptions kotlin.collections  
IONFILEUri kotlin.collections  
JSONException kotlin.collections  JSObject kotlin.collections  LegacyFilesystemImplementation kotlin.collections  List kotlin.collections  Log kotlin.collections  Logger kotlin.collections  Looper kotlin.collections  Manifest kotlin.collections  MediaScannerConnection kotlin.collections  NumberFormatException kotlin.collections  OUTPUT_CREATED_TIME kotlin.collections  OUTPUT_MODIFIED_TIME kotlin.collections  OUTPUT_NAME kotlin.collections  OUTPUT_SIZE kotlin.collections  OUTPUT_TYPE kotlin.collections  
OUTPUT_URI kotlin.collections  PERMISSION_GRANTED kotlin.collections  PUBLIC_STORAGE kotlin.collections  PUBLIC_STORAGE_ABOVE_ANDROID_10 kotlin.collections  PermissionState kotlin.collections  PluginMethod kotlin.collections  ProgressEmitter kotlin.collections  ReadFileInChunksOptions kotlin.collections  ReadFileOptions kotlin.collections  SingleUriWithRecursiveOptions kotlin.collections  System kotlin.collections  Throws kotlin.collections  URISyntaxException kotlin.collections  URL kotlin.collections  Uri kotlin.collections  WriteFileOptions kotlin.collections  also kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  cancel kotlin.collections  catch kotlin.collections  context kotlin.collections  
controller kotlin.collections  createReadDirResultObject kotlin.collections  createReadResultObject kotlin.collections  createUriResultObject kotlin.collections  createWriteResultObject kotlin.collections  createdTimestamp kotlin.collections  getDoubleIONFILEUri kotlin.collections  getReadFileInChunksOptions kotlin.collections  getReadFileOptions kotlin.collections  getSingleIONFILEUri kotlin.collections   getSingleUriWithRecursiveOptions kotlin.collections  getValue kotlin.collections  getWriteFileOptions kotlin.collections  
isNotBlank kotlin.collections  isStoragePermissionGranted kotlin.collections  lastModifiedTimestamp kotlin.collections  launch kotlin.collections  launchIn kotlin.collections  lazy kotlin.collections  legacyImplementation kotlin.collections  let kotlin.collections  map kotlin.collections  name kotlin.collections  onCompletion kotlin.collections  onEach kotlin.collections  	onFailure kotlin.collections  	onSuccess kotlin.collections  padStart kotlin.collections  
plusAssign kotlin.collections  provideDelegate kotlin.collections  requestAllPermissions kotlin.collections  run kotlin.collections  	sendError kotlin.collections  sendSuccess kotlin.collections  size kotlin.collections  takeIf kotlin.collections  thread kotlin.collections  toFilesystemError kotlin.collections  toInt kotlin.collections  toResultObject kotlin.collections  type kotlin.collections  
unresolvedUri kotlin.collections  	uppercase kotlin.collections  uri kotlin.collections  getMAP kotlin.collections.List  getMap kotlin.collections.List  Build kotlin.comparisons  	ByteArray kotlin.comparisons  CoroutineScope kotlin.comparisons  Dispatchers kotlin.comparisons  	DoubleUri kotlin.comparisons  Environment kotlin.comparisons  	ErrorInfo kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  FileOutputStream kotlin.comparisons  FilesystemErrors kotlin.comparisons  Handler kotlin.comparisons  HttpURLConnectionBuilder kotlin.comparisons  INPUT_APPEND kotlin.comparisons  INPUT_CHUNK_SIZE kotlin.comparisons  
INPUT_DATA kotlin.comparisons  INPUT_DIRECTORY kotlin.comparisons  INPUT_ENCODING kotlin.comparisons  
INPUT_FROM kotlin.comparisons  INPUT_FROM_DIRECTORY kotlin.comparisons  
INPUT_PATH kotlin.comparisons  INPUT_RECURSIVE kotlin.comparisons  INPUT_TO kotlin.comparisons  INPUT_TO_DIRECTORY kotlin.comparisons  IOException kotlin.comparisons  IONFILEController kotlin.comparisons  IONFILECreateOptions kotlin.comparisons  IONFILEDeleteOptions kotlin.comparisons  IONFILEEncoding kotlin.comparisons  IONFILEFolderType kotlin.comparisons  IONFILEReadInChunksOptions kotlin.comparisons  IONFILEReadOptions kotlin.comparisons  IONFILESaveMode kotlin.comparisons  IONFILESaveOptions kotlin.comparisons  
IONFILEUri kotlin.comparisons  
JSONException kotlin.comparisons  JSObject kotlin.comparisons  LegacyFilesystemImplementation kotlin.comparisons  Log kotlin.comparisons  Logger kotlin.comparisons  Looper kotlin.comparisons  Manifest kotlin.comparisons  MediaScannerConnection kotlin.comparisons  NumberFormatException kotlin.comparisons  OUTPUT_CREATED_TIME kotlin.comparisons  OUTPUT_MODIFIED_TIME kotlin.comparisons  OUTPUT_NAME kotlin.comparisons  OUTPUT_SIZE kotlin.comparisons  OUTPUT_TYPE kotlin.comparisons  
OUTPUT_URI kotlin.comparisons  PERMISSION_GRANTED kotlin.comparisons  PUBLIC_STORAGE kotlin.comparisons  PUBLIC_STORAGE_ABOVE_ANDROID_10 kotlin.comparisons  PermissionState kotlin.comparisons  PluginMethod kotlin.comparisons  ProgressEmitter kotlin.comparisons  ReadFileInChunksOptions kotlin.comparisons  ReadFileOptions kotlin.comparisons  SingleUriWithRecursiveOptions kotlin.comparisons  System kotlin.comparisons  Throws kotlin.comparisons  URISyntaxException kotlin.comparisons  URL kotlin.comparisons  Uri kotlin.comparisons  WriteFileOptions kotlin.comparisons  also kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  cancel kotlin.comparisons  catch kotlin.comparisons  context kotlin.comparisons  
controller kotlin.comparisons  createReadDirResultObject kotlin.comparisons  createReadResultObject kotlin.comparisons  createUriResultObject kotlin.comparisons  createWriteResultObject kotlin.comparisons  createdTimestamp kotlin.comparisons  getDoubleIONFILEUri kotlin.comparisons  getReadFileInChunksOptions kotlin.comparisons  getReadFileOptions kotlin.comparisons  getSingleIONFILEUri kotlin.comparisons   getSingleUriWithRecursiveOptions kotlin.comparisons  getValue kotlin.comparisons  getWriteFileOptions kotlin.comparisons  
isNotBlank kotlin.comparisons  isStoragePermissionGranted kotlin.comparisons  lastModifiedTimestamp kotlin.comparisons  launch kotlin.comparisons  launchIn kotlin.comparisons  lazy kotlin.comparisons  legacyImplementation kotlin.comparisons  let kotlin.comparisons  map kotlin.comparisons  name kotlin.comparisons  onCompletion kotlin.comparisons  onEach kotlin.comparisons  	onFailure kotlin.comparisons  	onSuccess kotlin.comparisons  padStart kotlin.comparisons  
plusAssign kotlin.comparisons  provideDelegate kotlin.comparisons  requestAllPermissions kotlin.comparisons  run kotlin.comparisons  	sendError kotlin.comparisons  sendSuccess kotlin.comparisons  size kotlin.comparisons  takeIf kotlin.comparisons  thread kotlin.comparisons  toFilesystemError kotlin.comparisons  toInt kotlin.comparisons  toResultObject kotlin.comparisons  type kotlin.comparisons  
unresolvedUri kotlin.comparisons  	uppercase kotlin.comparisons  uri kotlin.comparisons  thread kotlin.concurrent  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  Build 	kotlin.io  	ByteArray 	kotlin.io  CoroutineScope 	kotlin.io  Dispatchers 	kotlin.io  	DoubleUri 	kotlin.io  Environment 	kotlin.io  	ErrorInfo 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  FileOutputStream 	kotlin.io  FilesystemErrors 	kotlin.io  Handler 	kotlin.io  HttpURLConnectionBuilder 	kotlin.io  INPUT_APPEND 	kotlin.io  INPUT_CHUNK_SIZE 	kotlin.io  
INPUT_DATA 	kotlin.io  INPUT_DIRECTORY 	kotlin.io  INPUT_ENCODING 	kotlin.io  
INPUT_FROM 	kotlin.io  INPUT_FROM_DIRECTORY 	kotlin.io  
INPUT_PATH 	kotlin.io  INPUT_RECURSIVE 	kotlin.io  INPUT_TO 	kotlin.io  INPUT_TO_DIRECTORY 	kotlin.io  IOException 	kotlin.io  IONFILEController 	kotlin.io  IONFILECreateOptions 	kotlin.io  IONFILEDeleteOptions 	kotlin.io  IONFILEEncoding 	kotlin.io  IONFILEFolderType 	kotlin.io  IONFILEReadInChunksOptions 	kotlin.io  IONFILEReadOptions 	kotlin.io  IONFILESaveMode 	kotlin.io  IONFILESaveOptions 	kotlin.io  
IONFILEUri 	kotlin.io  
JSONException 	kotlin.io  JSObject 	kotlin.io  LegacyFilesystemImplementation 	kotlin.io  Log 	kotlin.io  Logger 	kotlin.io  Looper 	kotlin.io  Manifest 	kotlin.io  MediaScannerConnection 	kotlin.io  NumberFormatException 	kotlin.io  OUTPUT_CREATED_TIME 	kotlin.io  OUTPUT_MODIFIED_TIME 	kotlin.io  OUTPUT_NAME 	kotlin.io  OUTPUT_SIZE 	kotlin.io  OUTPUT_TYPE 	kotlin.io  
OUTPUT_URI 	kotlin.io  PERMISSION_GRANTED 	kotlin.io  PUBLIC_STORAGE 	kotlin.io  PUBLIC_STORAGE_ABOVE_ANDROID_10 	kotlin.io  PermissionState 	kotlin.io  PluginMethod 	kotlin.io  ProgressEmitter 	kotlin.io  ReadFileInChunksOptions 	kotlin.io  ReadFileOptions 	kotlin.io  SingleUriWithRecursiveOptions 	kotlin.io  System 	kotlin.io  Throws 	kotlin.io  URISyntaxException 	kotlin.io  URL 	kotlin.io  Uri 	kotlin.io  WriteFileOptions 	kotlin.io  also 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  cancel 	kotlin.io  catch 	kotlin.io  context 	kotlin.io  
controller 	kotlin.io  createReadDirResultObject 	kotlin.io  createReadResultObject 	kotlin.io  createUriResultObject 	kotlin.io  createWriteResultObject 	kotlin.io  createdTimestamp 	kotlin.io  getDoubleIONFILEUri 	kotlin.io  getReadFileInChunksOptions 	kotlin.io  getReadFileOptions 	kotlin.io  getSingleIONFILEUri 	kotlin.io   getSingleUriWithRecursiveOptions 	kotlin.io  getValue 	kotlin.io  getWriteFileOptions 	kotlin.io  
isNotBlank 	kotlin.io  isStoragePermissionGranted 	kotlin.io  lastModifiedTimestamp 	kotlin.io  launch 	kotlin.io  launchIn 	kotlin.io  lazy 	kotlin.io  legacyImplementation 	kotlin.io  let 	kotlin.io  map 	kotlin.io  name 	kotlin.io  onCompletion 	kotlin.io  onEach 	kotlin.io  	onFailure 	kotlin.io  	onSuccess 	kotlin.io  padStart 	kotlin.io  
plusAssign 	kotlin.io  provideDelegate 	kotlin.io  requestAllPermissions 	kotlin.io  run 	kotlin.io  	sendError 	kotlin.io  sendSuccess 	kotlin.io  size 	kotlin.io  takeIf 	kotlin.io  thread 	kotlin.io  toFilesystemError 	kotlin.io  toInt 	kotlin.io  toResultObject 	kotlin.io  type 	kotlin.io  
unresolvedUri 	kotlin.io  	uppercase 	kotlin.io  uri 	kotlin.io  Build 
kotlin.jvm  	ByteArray 
kotlin.jvm  CoroutineScope 
kotlin.jvm  Dispatchers 
kotlin.jvm  	DoubleUri 
kotlin.jvm  Environment 
kotlin.jvm  	ErrorInfo 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  FileOutputStream 
kotlin.jvm  FilesystemErrors 
kotlin.jvm  Handler 
kotlin.jvm  HttpURLConnectionBuilder 
kotlin.jvm  INPUT_APPEND 
kotlin.jvm  INPUT_CHUNK_SIZE 
kotlin.jvm  
INPUT_DATA 
kotlin.jvm  INPUT_DIRECTORY 
kotlin.jvm  INPUT_ENCODING 
kotlin.jvm  
INPUT_FROM 
kotlin.jvm  INPUT_FROM_DIRECTORY 
kotlin.jvm  
INPUT_PATH 
kotlin.jvm  INPUT_RECURSIVE 
kotlin.jvm  INPUT_TO 
kotlin.jvm  INPUT_TO_DIRECTORY 
kotlin.jvm  IOException 
kotlin.jvm  IONFILEController 
kotlin.jvm  IONFILECreateOptions 
kotlin.jvm  IONFILEDeleteOptions 
kotlin.jvm  IONFILEEncoding 
kotlin.jvm  IONFILEFolderType 
kotlin.jvm  IONFILEReadInChunksOptions 
kotlin.jvm  IONFILEReadOptions 
kotlin.jvm  IONFILESaveMode 
kotlin.jvm  IONFILESaveOptions 
kotlin.jvm  
IONFILEUri 
kotlin.jvm  
JSONException 
kotlin.jvm  JSObject 
kotlin.jvm  LegacyFilesystemImplementation 
kotlin.jvm  Log 
kotlin.jvm  Logger 
kotlin.jvm  Looper 
kotlin.jvm  Manifest 
kotlin.jvm  MediaScannerConnection 
kotlin.jvm  NumberFormatException 
kotlin.jvm  OUTPUT_CREATED_TIME 
kotlin.jvm  OUTPUT_MODIFIED_TIME 
kotlin.jvm  OUTPUT_NAME 
kotlin.jvm  OUTPUT_SIZE 
kotlin.jvm  OUTPUT_TYPE 
kotlin.jvm  
OUTPUT_URI 
kotlin.jvm  PERMISSION_GRANTED 
kotlin.jvm  PUBLIC_STORAGE 
kotlin.jvm  PUBLIC_STORAGE_ABOVE_ANDROID_10 
kotlin.jvm  PermissionState 
kotlin.jvm  PluginMethod 
kotlin.jvm  ProgressEmitter 
kotlin.jvm  ReadFileInChunksOptions 
kotlin.jvm  ReadFileOptions 
kotlin.jvm  SingleUriWithRecursiveOptions 
kotlin.jvm  System 
kotlin.jvm  Throws 
kotlin.jvm  URISyntaxException 
kotlin.jvm  URL 
kotlin.jvm  Uri 
kotlin.jvm  WriteFileOptions 
kotlin.jvm  also 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  cancel 
kotlin.jvm  catch 
kotlin.jvm  context 
kotlin.jvm  
controller 
kotlin.jvm  createReadDirResultObject 
kotlin.jvm  createReadResultObject 
kotlin.jvm  createUriResultObject 
kotlin.jvm  createWriteResultObject 
kotlin.jvm  createdTimestamp 
kotlin.jvm  getDoubleIONFILEUri 
kotlin.jvm  getReadFileInChunksOptions 
kotlin.jvm  getReadFileOptions 
kotlin.jvm  getSingleIONFILEUri 
kotlin.jvm   getSingleUriWithRecursiveOptions 
kotlin.jvm  getValue 
kotlin.jvm  getWriteFileOptions 
kotlin.jvm  
isNotBlank 
kotlin.jvm  isStoragePermissionGranted 
kotlin.jvm  lastModifiedTimestamp 
kotlin.jvm  launch 
kotlin.jvm  launchIn 
kotlin.jvm  lazy 
kotlin.jvm  legacyImplementation 
kotlin.jvm  let 
kotlin.jvm  map 
kotlin.jvm  name 
kotlin.jvm  onCompletion 
kotlin.jvm  onEach 
kotlin.jvm  	onFailure 
kotlin.jvm  	onSuccess 
kotlin.jvm  padStart 
kotlin.jvm  
plusAssign 
kotlin.jvm  provideDelegate 
kotlin.jvm  requestAllPermissions 
kotlin.jvm  run 
kotlin.jvm  	sendError 
kotlin.jvm  sendSuccess 
kotlin.jvm  size 
kotlin.jvm  takeIf 
kotlin.jvm  thread 
kotlin.jvm  toFilesystemError 
kotlin.jvm  toInt 
kotlin.jvm  toResultObject 
kotlin.jvm  type 
kotlin.jvm  
unresolvedUri 
kotlin.jvm  	uppercase 
kotlin.jvm  uri 
kotlin.jvm  Build 
kotlin.ranges  	ByteArray 
kotlin.ranges  CoroutineScope 
kotlin.ranges  Dispatchers 
kotlin.ranges  	DoubleUri 
kotlin.ranges  Environment 
kotlin.ranges  	ErrorInfo 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  FileOutputStream 
kotlin.ranges  FilesystemErrors 
kotlin.ranges  Handler 
kotlin.ranges  HttpURLConnectionBuilder 
kotlin.ranges  INPUT_APPEND 
kotlin.ranges  INPUT_CHUNK_SIZE 
kotlin.ranges  
INPUT_DATA 
kotlin.ranges  INPUT_DIRECTORY 
kotlin.ranges  INPUT_ENCODING 
kotlin.ranges  
INPUT_FROM 
kotlin.ranges  INPUT_FROM_DIRECTORY 
kotlin.ranges  
INPUT_PATH 
kotlin.ranges  INPUT_RECURSIVE 
kotlin.ranges  INPUT_TO 
kotlin.ranges  INPUT_TO_DIRECTORY 
kotlin.ranges  IOException 
kotlin.ranges  IONFILEController 
kotlin.ranges  IONFILECreateOptions 
kotlin.ranges  IONFILEDeleteOptions 
kotlin.ranges  IONFILEEncoding 
kotlin.ranges  IONFILEFolderType 
kotlin.ranges  IONFILEReadInChunksOptions 
kotlin.ranges  IONFILEReadOptions 
kotlin.ranges  IONFILESaveMode 
kotlin.ranges  IONFILESaveOptions 
kotlin.ranges  
IONFILEUri 
kotlin.ranges  
JSONException 
kotlin.ranges  JSObject 
kotlin.ranges  LegacyFilesystemImplementation 
kotlin.ranges  Log 
kotlin.ranges  Logger 
kotlin.ranges  Looper 
kotlin.ranges  Manifest 
kotlin.ranges  MediaScannerConnection 
kotlin.ranges  NumberFormatException 
kotlin.ranges  OUTPUT_CREATED_TIME 
kotlin.ranges  OUTPUT_MODIFIED_TIME 
kotlin.ranges  OUTPUT_NAME 
kotlin.ranges  OUTPUT_SIZE 
kotlin.ranges  OUTPUT_TYPE 
kotlin.ranges  
OUTPUT_URI 
kotlin.ranges  PERMISSION_GRANTED 
kotlin.ranges  PUBLIC_STORAGE 
kotlin.ranges  PUBLIC_STORAGE_ABOVE_ANDROID_10 
kotlin.ranges  PermissionState 
kotlin.ranges  PluginMethod 
kotlin.ranges  ProgressEmitter 
kotlin.ranges  ReadFileInChunksOptions 
kotlin.ranges  ReadFileOptions 
kotlin.ranges  SingleUriWithRecursiveOptions 
kotlin.ranges  System 
kotlin.ranges  Throws 
kotlin.ranges  URISyntaxException 
kotlin.ranges  URL 
kotlin.ranges  Uri 
kotlin.ranges  WriteFileOptions 
kotlin.ranges  also 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  cancel 
kotlin.ranges  catch 
kotlin.ranges  context 
kotlin.ranges  
controller 
kotlin.ranges  createReadDirResultObject 
kotlin.ranges  createReadResultObject 
kotlin.ranges  createUriResultObject 
kotlin.ranges  createWriteResultObject 
kotlin.ranges  createdTimestamp 
kotlin.ranges  getDoubleIONFILEUri 
kotlin.ranges  getReadFileInChunksOptions 
kotlin.ranges  getReadFileOptions 
kotlin.ranges  getSingleIONFILEUri 
kotlin.ranges   getSingleUriWithRecursiveOptions 
kotlin.ranges  getValue 
kotlin.ranges  getWriteFileOptions 
kotlin.ranges  
isNotBlank 
kotlin.ranges  isStoragePermissionGranted 
kotlin.ranges  lastModifiedTimestamp 
kotlin.ranges  launch 
kotlin.ranges  launchIn 
kotlin.ranges  lazy 
kotlin.ranges  legacyImplementation 
kotlin.ranges  let 
kotlin.ranges  map 
kotlin.ranges  name 
kotlin.ranges  onCompletion 
kotlin.ranges  onEach 
kotlin.ranges  	onFailure 
kotlin.ranges  	onSuccess 
kotlin.ranges  padStart 
kotlin.ranges  
plusAssign 
kotlin.ranges  provideDelegate 
kotlin.ranges  requestAllPermissions 
kotlin.ranges  run 
kotlin.ranges  	sendError 
kotlin.ranges  sendSuccess 
kotlin.ranges  size 
kotlin.ranges  takeIf 
kotlin.ranges  thread 
kotlin.ranges  toFilesystemError 
kotlin.ranges  toInt 
kotlin.ranges  toResultObject 
kotlin.ranges  type 
kotlin.ranges  
unresolvedUri 
kotlin.ranges  	uppercase 
kotlin.ranges  uri 
kotlin.ranges  KClass kotlin.reflect  
KFunction1 kotlin.reflect  Build kotlin.sequences  	ByteArray kotlin.sequences  CoroutineScope kotlin.sequences  Dispatchers kotlin.sequences  	DoubleUri kotlin.sequences  Environment kotlin.sequences  	ErrorInfo kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  FileOutputStream kotlin.sequences  FilesystemErrors kotlin.sequences  Handler kotlin.sequences  HttpURLConnectionBuilder kotlin.sequences  INPUT_APPEND kotlin.sequences  INPUT_CHUNK_SIZE kotlin.sequences  
INPUT_DATA kotlin.sequences  INPUT_DIRECTORY kotlin.sequences  INPUT_ENCODING kotlin.sequences  
INPUT_FROM kotlin.sequences  INPUT_FROM_DIRECTORY kotlin.sequences  
INPUT_PATH kotlin.sequences  INPUT_RECURSIVE kotlin.sequences  INPUT_TO kotlin.sequences  INPUT_TO_DIRECTORY kotlin.sequences  IOException kotlin.sequences  IONFILEController kotlin.sequences  IONFILECreateOptions kotlin.sequences  IONFILEDeleteOptions kotlin.sequences  IONFILEEncoding kotlin.sequences  IONFILEFolderType kotlin.sequences  IONFILEReadInChunksOptions kotlin.sequences  IONFILEReadOptions kotlin.sequences  IONFILESaveMode kotlin.sequences  IONFILESaveOptions kotlin.sequences  
IONFILEUri kotlin.sequences  
JSONException kotlin.sequences  JSObject kotlin.sequences  LegacyFilesystemImplementation kotlin.sequences  Log kotlin.sequences  Logger kotlin.sequences  Looper kotlin.sequences  Manifest kotlin.sequences  MediaScannerConnection kotlin.sequences  NumberFormatException kotlin.sequences  OUTPUT_CREATED_TIME kotlin.sequences  OUTPUT_MODIFIED_TIME kotlin.sequences  OUTPUT_NAME kotlin.sequences  OUTPUT_SIZE kotlin.sequences  OUTPUT_TYPE kotlin.sequences  
OUTPUT_URI kotlin.sequences  PERMISSION_GRANTED kotlin.sequences  PUBLIC_STORAGE kotlin.sequences  PUBLIC_STORAGE_ABOVE_ANDROID_10 kotlin.sequences  PermissionState kotlin.sequences  PluginMethod kotlin.sequences  ProgressEmitter kotlin.sequences  ReadFileInChunksOptions kotlin.sequences  ReadFileOptions kotlin.sequences  SingleUriWithRecursiveOptions kotlin.sequences  System kotlin.sequences  Throws kotlin.sequences  URISyntaxException kotlin.sequences  URL kotlin.sequences  Uri kotlin.sequences  WriteFileOptions kotlin.sequences  also kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  cancel kotlin.sequences  catch kotlin.sequences  context kotlin.sequences  
controller kotlin.sequences  createReadDirResultObject kotlin.sequences  createReadResultObject kotlin.sequences  createUriResultObject kotlin.sequences  createWriteResultObject kotlin.sequences  createdTimestamp kotlin.sequences  getDoubleIONFILEUri kotlin.sequences  getReadFileInChunksOptions kotlin.sequences  getReadFileOptions kotlin.sequences  getSingleIONFILEUri kotlin.sequences   getSingleUriWithRecursiveOptions kotlin.sequences  getValue kotlin.sequences  getWriteFileOptions kotlin.sequences  
isNotBlank kotlin.sequences  isStoragePermissionGranted kotlin.sequences  lastModifiedTimestamp kotlin.sequences  launch kotlin.sequences  launchIn kotlin.sequences  lazy kotlin.sequences  legacyImplementation kotlin.sequences  let kotlin.sequences  map kotlin.sequences  name kotlin.sequences  onCompletion kotlin.sequences  onEach kotlin.sequences  	onFailure kotlin.sequences  	onSuccess kotlin.sequences  padStart kotlin.sequences  
plusAssign kotlin.sequences  provideDelegate kotlin.sequences  requestAllPermissions kotlin.sequences  run kotlin.sequences  	sendError kotlin.sequences  sendSuccess kotlin.sequences  size kotlin.sequences  takeIf kotlin.sequences  thread kotlin.sequences  toFilesystemError kotlin.sequences  toInt kotlin.sequences  toResultObject kotlin.sequences  type kotlin.sequences  
unresolvedUri kotlin.sequences  	uppercase kotlin.sequences  uri kotlin.sequences  Build kotlin.text  	ByteArray kotlin.text  CoroutineScope kotlin.text  Dispatchers kotlin.text  	DoubleUri kotlin.text  Environment kotlin.text  	ErrorInfo kotlin.text  	Exception kotlin.text  File kotlin.text  FileOutputStream kotlin.text  FilesystemErrors kotlin.text  Handler kotlin.text  HttpURLConnectionBuilder kotlin.text  INPUT_APPEND kotlin.text  INPUT_CHUNK_SIZE kotlin.text  
INPUT_DATA kotlin.text  INPUT_DIRECTORY kotlin.text  INPUT_ENCODING kotlin.text  
INPUT_FROM kotlin.text  INPUT_FROM_DIRECTORY kotlin.text  
INPUT_PATH kotlin.text  INPUT_RECURSIVE kotlin.text  INPUT_TO kotlin.text  INPUT_TO_DIRECTORY kotlin.text  IOException kotlin.text  IONFILEController kotlin.text  IONFILECreateOptions kotlin.text  IONFILEDeleteOptions kotlin.text  IONFILEEncoding kotlin.text  IONFILEFolderType kotlin.text  IONFILEReadInChunksOptions kotlin.text  IONFILEReadOptions kotlin.text  IONFILESaveMode kotlin.text  IONFILESaveOptions kotlin.text  
IONFILEUri kotlin.text  
JSONException kotlin.text  JSObject kotlin.text  LegacyFilesystemImplementation kotlin.text  Log kotlin.text  Logger kotlin.text  Looper kotlin.text  Manifest kotlin.text  MediaScannerConnection kotlin.text  NumberFormatException kotlin.text  OUTPUT_CREATED_TIME kotlin.text  OUTPUT_MODIFIED_TIME kotlin.text  OUTPUT_NAME kotlin.text  OUTPUT_SIZE kotlin.text  OUTPUT_TYPE kotlin.text  
OUTPUT_URI kotlin.text  PERMISSION_GRANTED kotlin.text  PUBLIC_STORAGE kotlin.text  PUBLIC_STORAGE_ABOVE_ANDROID_10 kotlin.text  PermissionState kotlin.text  PluginMethod kotlin.text  ProgressEmitter kotlin.text  ReadFileInChunksOptions kotlin.text  ReadFileOptions kotlin.text  SingleUriWithRecursiveOptions kotlin.text  System kotlin.text  Throws kotlin.text  URISyntaxException kotlin.text  URL kotlin.text  Uri kotlin.text  WriteFileOptions kotlin.text  also kotlin.text  apply kotlin.text  arrayOf kotlin.text  cancel kotlin.text  catch kotlin.text  context kotlin.text  
controller kotlin.text  createReadDirResultObject kotlin.text  createReadResultObject kotlin.text  createUriResultObject kotlin.text  createWriteResultObject kotlin.text  createdTimestamp kotlin.text  getDoubleIONFILEUri kotlin.text  getReadFileInChunksOptions kotlin.text  getReadFileOptions kotlin.text  getSingleIONFILEUri kotlin.text   getSingleUriWithRecursiveOptions kotlin.text  getValue kotlin.text  getWriteFileOptions kotlin.text  
isNotBlank kotlin.text  isStoragePermissionGranted kotlin.text  lastModifiedTimestamp kotlin.text  launch kotlin.text  launchIn kotlin.text  lazy kotlin.text  legacyImplementation kotlin.text  let kotlin.text  map kotlin.text  name kotlin.text  onCompletion kotlin.text  onEach kotlin.text  	onFailure kotlin.text  	onSuccess kotlin.text  padStart kotlin.text  
plusAssign kotlin.text  provideDelegate kotlin.text  requestAllPermissions kotlin.text  run kotlin.text  	sendError kotlin.text  sendSuccess kotlin.text  size kotlin.text  takeIf kotlin.text  thread kotlin.text  toFilesystemError kotlin.text  toInt kotlin.text  toResultObject kotlin.text  type kotlin.text  
unresolvedUri kotlin.text  	uppercase kotlin.text  uri kotlin.text  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  cancel kotlinx.coroutines  launch kotlinx.coroutines  cancel !kotlinx.coroutines.CoroutineScope  
controller !kotlinx.coroutines.CoroutineScope  	getCANCEL !kotlinx.coroutines.CoroutineScope  
getCONTROLLER !kotlinx.coroutines.CoroutineScope  	getCancel !kotlinx.coroutines.CoroutineScope  
getController !kotlinx.coroutines.CoroutineScope  getISStoragePermissionGranted !kotlinx.coroutines.CoroutineScope  getIsStoragePermissionGranted !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getONFailure !kotlinx.coroutines.CoroutineScope  getONSuccess !kotlinx.coroutines.CoroutineScope  getOnFailure !kotlinx.coroutines.CoroutineScope  getOnSuccess !kotlinx.coroutines.CoroutineScope  getREQUESTAllPermissions !kotlinx.coroutines.CoroutineScope  getRequestAllPermissions !kotlinx.coroutines.CoroutineScope  getSENDError !kotlinx.coroutines.CoroutineScope  getSENDSuccess !kotlinx.coroutines.CoroutineScope  getSendError !kotlinx.coroutines.CoroutineScope  getSendSuccess !kotlinx.coroutines.CoroutineScope  getTOFilesystemError !kotlinx.coroutines.CoroutineScope  getTOResultObject !kotlinx.coroutines.CoroutineScope  getToFilesystemError !kotlinx.coroutines.CoroutineScope  getToResultObject !kotlinx.coroutines.CoroutineScope  isStoragePermissionGranted !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  requestAllPermissions !kotlinx.coroutines.CoroutineScope  	sendError !kotlinx.coroutines.CoroutineScope  sendSuccess !kotlinx.coroutines.CoroutineScope  toFilesystemError !kotlinx.coroutines.CoroutineScope  toResultObject !kotlinx.coroutines.CoroutineScope  Main kotlinx.coroutines.Dispatchers  
FlowCollector kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  launchIn kotlinx.coroutines.flow  onCompletion kotlinx.coroutines.flow  onEach kotlinx.coroutines.flow  catch kotlinx.coroutines.flow.Flow  getCATCH kotlinx.coroutines.flow.Flow  getCatch kotlinx.coroutines.flow.Flow  getLAUNCHIn kotlinx.coroutines.flow.Flow  getLaunchIn kotlinx.coroutines.flow.Flow  getONCompletion kotlinx.coroutines.flow.Flow  	getONEach kotlinx.coroutines.flow.Flow  getOnCompletion kotlinx.coroutines.flow.Flow  	getOnEach kotlinx.coroutines.flow.Flow  launchIn kotlinx.coroutines.flow.Flow  onCompletion kotlinx.coroutines.flow.Flow  onEach kotlinx.coroutines.flow.Flow  createReadResultObject %kotlinx.coroutines.flow.FlowCollector  getCREATEReadResultObject %kotlinx.coroutines.flow.FlowCollector  getCreateReadResultObject %kotlinx.coroutines.flow.FlowCollector  getSENDError %kotlinx.coroutines.flow.FlowCollector  getSENDSuccess %kotlinx.coroutines.flow.FlowCollector  getSendError %kotlinx.coroutines.flow.FlowCollector  getSendSuccess %kotlinx.coroutines.flow.FlowCollector  getTOFilesystemError %kotlinx.coroutines.flow.FlowCollector  getToFilesystemError %kotlinx.coroutines.flow.FlowCollector  	sendError %kotlinx.coroutines.flow.FlowCollector  sendSuccess %kotlinx.coroutines.flow.FlowCollector  toFilesystemError %kotlinx.coroutines.flow.FlowCollector  
JSONException org.json  
JSONObject org.json  getLOCALIZEDMessage org.json.JSONException  getLocalizedMessage org.json.JSONException  localizedMessage org.json.JSONException  setLocalizedMessage org.json.JSONException  also org.json.JSONObject  apply org.json.JSONObject  	getString org.json.JSONObject  put org.json.JSONObject  putOpt org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
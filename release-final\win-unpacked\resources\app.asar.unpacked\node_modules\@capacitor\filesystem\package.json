{"name": "@capacitor/filesystem", "version": "7.1.1", "description": "The Filesystem API provides a NodeJS-like API for working with files on the device.", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Sources", "ios/Tests", "Package.swift", "CapacitorFilesystem.podspec"], "author": "Outsystems", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ionic-team/capacitor-filesystem.git"}, "dependencies": {"@capacitor/synapse": "^1.0.1"}, "devDependencies": {"@capacitor/android": "7.0.1", "@capacitor/core": "7.0.1", "@capacitor/docgen": "^0.2.2", "@capacitor/ios": "7.0.1", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "^4.0.0", "@ionic/swiftlint-config": "^2.0.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/npm": "^12.0.1", "@types/node": "^20.14.8", "eslint": "^8.57.0", "prettier": "^3.3.3", "prettier-plugin-java": "^2.6.4", "rimraf": "^6.0.1", "rollup": "^2.78.1", "semantic-release": "^24.0.0", "swiftlint": "^2.0.0", "typescript": "~5.4.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}
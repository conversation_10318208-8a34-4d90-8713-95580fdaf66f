import { isMobile } from '@/utils/platform';
import { storageService } from './storage';
import { csvStorageService } from './csv-storage';
import { sqliteStorageService } from './sqlite-storage';
import { storageMigrationService } from './storage-migration';
import { mobileSQLiteStorageService } from './mobile-sqlite-storage';
import { mobileStorageMigrationService } from './mobile-storage-migration';

/**
 * Adaptive storage service that automatically chooses between:
 * - Desktop: SQLite database for high performance
 * - Mobile: SQLite database for high performance (with CSV fallback)
 */
class AdaptiveStorageService {
  private migrationChecked = false;
  private mobileMigrationChecked = false;

  private get storage() {
    return isMobile() ? csvStorageService : storageService;
  }

  private get sqliteStorage() {
    return isMobile() ? mobileSQLiteStorageService : sqliteStorageService;
  }

  /**
   * Check and perform migration from CSV to SQLite on desktop
   */
  private async checkDesktopMigration(): Promise<void> {
    if (this.migrationChecked || isMobile()) {
      return;
    }

    this.migrationChecked = true;

    try {
      // For now, skip SQLite migration until IPC is properly set up
      // The app will continue using CSV storage on desktop
      console.log('SQLite migration temporarily disabled - using CSV storage');
      return;

      // TODO: Enable this when IPC is set up
      // if (!storageMigrationService.isMigrationCompleted()) {
      //   console.log('Starting automatic desktop migration to SQLite...');
      //   const result = await storageMigrationService.migrateToSQLite();
      //
      //   if (result.success) {
      //     console.log('Desktop migration completed successfully:', result.message);
      //   } else {
      //     console.error('Desktop migration failed:', result.message, result.errors);
      //   }
      // }
    } catch (error) {
      console.error('Desktop migration check failed:', error);
    }
  }

  /**
   * Check and perform migration from CSV to SQLite on mobile
   */
  private async checkMobileMigration(): Promise<void> {
    if (this.mobileMigrationChecked || !isMobile()) {
      return;
    }

    this.mobileMigrationChecked = true;

    try {
      if (!(await mobileStorageMigrationService.isMigrationCompleted())) {
        console.log('Starting automatic mobile migration to SQLite...');
        const result = await mobileStorageMigrationService.migrateToSQLite();

        if (result.success) {
          console.log('Mobile migration completed successfully:', result.message);
        } else {
          console.error('Mobile migration failed:', result.message, result.errors);
          // Fall back to CSV storage on mobile if SQLite migration fails
          console.log('Falling back to CSV storage on mobile');
        }
      }
    } catch (error) {
      console.error('Mobile migration check failed:', error);
      // Fall back to CSV storage on mobile if SQLite migration fails
      console.log('Falling back to CSV storage on mobile due to error');
    }
  }

  /**
   * Check and perform appropriate migration based on platform
   */
  private async checkMigration(): Promise<void> {
    if (isMobile()) {
      await this.checkMobileMigration();
    } else {
      await this.checkDesktopMigration();
    }
  }

  // Generic methods
  async set<T>(key: string, value: T): Promise<void> {
    if (isMobile()) {
      // CSV storage doesn't have generic set method, use specific methods
      console.warn('Generic set method not available in CSV storage');
    } else {
      storageService.set(key, value);
    }
  }

  async get<T>(key: string, defaultValue: T): Promise<T> {
    if (isMobile()) {
      // CSV storage doesn't have generic get method, use specific methods
      console.warn('Generic get method not available in CSV storage');
      return defaultValue;
    } else {
      return storageService.get(key, defaultValue);
    }
  }

  async remove(key: string): Promise<void> {
    if (isMobile()) {
      // CSV storage doesn't have generic remove method
      console.warn('Generic remove method not available in CSV storage');
    } else {
      storageService.remove(key);
    }
  }

  async clear(): Promise<void> {
    if (isMobile()) {
      // Clear mobile data using comprehensive method
      await this.clearMobileData();
    } else {
      // Clear desktop data using comprehensive method
      await this.clearDesktopData();
    }
  }

  /**
   * Comprehensive mobile data clearing
   */
  private async clearMobileData(): Promise<void> {
    try {
      // Clear CSV storage data
      await csvStorageService.clearAllData();

      // Clear mobile preferences
      const { Preferences } = await import('@capacitor/preferences');
      const { keys } = await Preferences.keys();
      const smartBoutiqueKeys = keys.filter(key =>
        key.startsWith('smartboutique_') ||
        key.startsWith('smartboutique_csv_')
      );

      for (const key of smartBoutiqueKeys) {
        await Preferences.remove({ key });
      }

      // Clear SQLite if migration was completed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          await this.sqliteStorage.clearAllData();
        }
      } catch (error) {
        console.warn('Mobile SQLite clear failed or not available:', error);
      }

      console.log('✅ Mobile data cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing mobile data:', error);
      throw error;
    }
  }

  /**
   * Comprehensive desktop data clearing
   */
  private async clearDesktopData(): Promise<void> {
    try {
      // Clear localStorage data
      storageService.clear();

      // Clear SQLite data if available
      try {
        sqliteStorageService.clearAllData();
      } catch (error) {
        console.warn('Desktop SQLite clear failed or not available:', error);
      }

      console.log('✅ Desktop data cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing desktop data:', error);
      throw error;
    }
  }

  // Specific data methods with SQLite support on both platforms
  async getUsers() {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          return await this.sqliteStorage.getUsers();
        }
      } catch (error) {
        console.warn('Mobile SQLite failed, falling back to CSV:', error);
      }
      return await csvStorageService.getUsers();
    } else {
      // For now, use CSV storage on desktop until SQLite IPC is set up
      return storageService.getUsers();
    }
  }

  async setUsers(users: any[]) {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          await this.sqliteStorage.setUsers(users);
          return;
        }
      } catch (error) {
        console.warn('Mobile SQLite failed, falling back to CSV:', error);
      }
      await csvStorageService.setUsers(users);
    } else {
      // For now, use CSV storage on desktop until SQLite IPC is set up
      storageService.setUsers(users);
    }
  }

  async getProducts() {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          return await this.sqliteStorage.getProducts();
        }
      } catch (error) {
        console.warn('Mobile SQLite failed, falling back to CSV:', error);
      }
      return await csvStorageService.getProducts();
    } else {
      // For now, use CSV storage on desktop until SQLite IPC is set up
      return storageService.getProducts();
    }
  }

  async setProducts(products: any[]) {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          await this.sqliteStorage.setProducts(products);
          return;
        }
      } catch (error) {
        console.warn('Mobile SQLite failed, falling back to CSV:', error);
      }
      await csvStorageService.setProducts(products);
    } else {
      // For now, use CSV storage on desktop until SQLite IPC is set up
      storageService.setProducts(products);
    }
  }

  async getSales() {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          return await this.sqliteStorage.getSales();
        }
      } catch (error) {
        console.warn('Mobile SQLite failed, falling back to CSV:', error);
      }
      return await csvStorageService.getSales();
    } else {
      // For now, use CSV storage on desktop until SQLite IPC is set up
      return storageService.getSales();
    }
  }

  async setSales(sales: any[]) {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          await this.sqliteStorage.setSales(sales);
          return;
        }
      } catch (error) {
        console.warn('Mobile SQLite failed, falling back to CSV:', error);
      }
      await csvStorageService.setSales(sales);
    } else {
      // For now, use CSV storage on desktop until SQLite IPC is set up
      storageService.setSales(sales);
    }
  }

  async getDebts() {
    if (isMobile()) {
      return await csvStorageService.getDebts();
    } else {
      // For now, keep using localStorage for debts until SQLite service is extended
      return storageService.getDebts();
    }
  }

  async setDebts(debts: any[]) {
    if (isMobile()) {
      await csvStorageService.setDebts(debts);
    } else {
      // For now, keep using localStorage for debts until SQLite service is extended
      storageService.setDebts(debts);
    }
  }

  // Legacy methods for backward compatibility
  async getDettes() {
    return this.getDebts();
  }

  async setDettes(debts: any[]) {
    await this.setDebts(debts);
  }

  async getCreances() {
    return this.getDebts();
  }

  async setCreances(debts: any[]) {
    await this.setDebts(debts);
  }

  async getExpenses() {
    if (isMobile()) {
      return await csvStorageService.getExpenses();
    } else {
      // For now, keep using localStorage for expenses until SQLite service is extended
      return storageService.getExpenses();
    }
  }

  async setExpenses(expenses: any[]) {
    if (isMobile()) {
      await csvStorageService.setExpenses(expenses);
    } else {
      // For now, keep using localStorage for expenses until SQLite service is extended
      storageService.setExpenses(expenses);
    }
  }

  // Employee Payments methods
  async getEmployeePayments() {
    if (isMobile()) {
      // For mobile, use CSV storage for now
      return await csvStorageService.getEmployeePayments?.() || [];
    } else {
      // For desktop, try SQLite first, fallback to localStorage
      try {
        return sqliteStorageService.getEmployeePayments();
      } catch (error) {
        console.warn('SQLite not available for employee payments, using localStorage:', error);
        return storageService.getEmployeePayments?.() || [];
      }
    }
  }

  async addEmployeePayment(payment: any) {
    if (isMobile()) {
      // For mobile, use CSV storage for now
      if (csvStorageService.addEmployeePayment) {
        await csvStorageService.addEmployeePayment(payment);
      }
    } else {
      // For desktop, try SQLite first, fallback to localStorage
      try {
        sqliteStorageService.addEmployeePayment(payment);
      } catch (error) {
        console.warn('SQLite not available for employee payments, using localStorage:', error);
        if (storageService.addEmployeePayment) {
          storageService.addEmployeePayment(payment);
        }
      }
    }
  }

  async updateEmployeePayment(payment: any) {
    if (isMobile()) {
      // For mobile, use CSV storage for now
      if (csvStorageService.updateEmployeePayment) {
        await csvStorageService.updateEmployeePayment(payment);
      }
    } else {
      // For desktop, try SQLite first, fallback to localStorage
      try {
        sqliteStorageService.updateEmployeePayment(payment);
      } catch (error) {
        console.warn('SQLite not available for employee payments, using localStorage:', error);
        if (storageService.updateEmployeePayment) {
          storageService.updateEmployeePayment(payment);
        }
      }
    }
  }

  async deleteEmployeePayment(id: string) {
    if (isMobile()) {
      // For mobile, use CSV storage for now
      if (csvStorageService.deleteEmployeePayment) {
        await csvStorageService.deleteEmployeePayment(id);
      }
    } else {
      // For desktop, try SQLite first, fallback to localStorage
      try {
        sqliteStorageService.deleteEmployeePayment(id);
      } catch (error) {
        console.warn('SQLite not available for employee payments, using localStorage:', error);
        if (storageService.deleteEmployeePayment) {
          storageService.deleteEmployeePayment(id);
        }
      }
    }
  }

  async setEmployeePayments(payments: any[]) {
    if (isMobile()) {
      // For mobile, use CSV storage for now
      if (csvStorageService.setEmployeePayments) {
        await csvStorageService.setEmployeePayments(payments);
      }
    } else {
      // For desktop, try SQLite first, fallback to localStorage
      try {
        sqliteStorageService.setEmployeePayments(payments);
      } catch (error) {
        console.warn('SQLite not available for employee payments, using localStorage:', error);
        if (storageService.setEmployeePayments) {
          storageService.setEmployeePayments(payments);
        }
      }
    }
  }

  async getSettings() {
    if (isMobile()) {
      return await csvStorageService.getSettings();
    } else {
      return storageService.getSettings();
    }
  }

  async setSettings(settings: any) {
    if (isMobile()) {
      await csvStorageService.setSettings(settings);
    } else {
      storageService.setSettings(settings);
    }
  }

  async getCurrentUser() {
    if (isMobile()) {
      return await csvStorageService.getCurrentUser();
    } else {
      return storageService.getCurrentUser();
    }
  }

  async setCurrentUser(user: any) {
    if (isMobile()) {
      await csvStorageService.setCurrentUser(user);
    } else {
      storageService.setCurrentUser(user);
    }
  }

  // Initialize default data with comprehensive demo content
  async initializeDefaultData() {
    console.log('🔄 Initializing fresh demo data...');

    if (isMobile()) {
      await csvStorageService.initializeDefaultData();
    } else {
      storageService.initializeDefaultData();
    }

    // Force check and initialize debt data if empty
    const debts = await this.getDebts();
    if (debts.length === 0) {
      await this.forceInitializeDebts();
    }

    // Initialize sample sales data if empty
    const sales = await this.getSales();
    if (sales.length === 0) {
      await this.initializeSampleSales();
    }

    // Initialize sample expenses if empty
    const expenses = await this.getExpenses();
    if (expenses.length === 0) {
      await this.initializeSampleExpenses();
    }

    console.log('✅ Fresh demo data initialized successfully');
  }

  /**
   * Initialize sample sales data
   */
  private async initializeSampleSales() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const twoDaysAgo = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);

    const sampleSales = [
      {
        id: '1',
        numeroRecu: 'RV-' + now.toISOString().slice(0, 10).replace(/-/g, '') + '-0001',
        datevente: yesterday.toISOString(),
        nomClient: 'Marie Kabila',
        telephoneClient: '+243 900 000 001',
        produits: [
          {
            produitId: '1',
            nomProduit: 'iPhone 15',
            quantite: 1,
            prixUnitaireCDF: 2240000,
            prixUnitaireUSD: 800,
            totalCDF: 2240000,
            totalUSD: 800
          }
        ],
        totalCDF: 2240000,
        totalUSD: 800,
        methodePaiement: 'cash',
        typeVente: 'cash',
        vendeur: 'Super Admin',
        notes: 'Vente comptant - Client satisfait'
      },
      {
        id: '2',
        numeroRecu: 'RV-' + now.toISOString().slice(0, 10).replace(/-/g, '') + '-0002',
        datevente: twoDaysAgo.toISOString(),
        nomClient: 'Jean Mukendi',
        telephoneClient: '+243 900 000 002',
        produits: [
          {
            produitId: '2',
            nomProduit: 'T-shirt Nike',
            quantite: 2,
            prixUnitaireCDF: 98000,
            prixUnitaireUSD: 35,
            totalCDF: 196000,
            totalUSD: 70
          }
        ],
        totalCDF: 196000,
        totalUSD: 70,
        methodePaiement: 'mobile_money',
        typeVente: 'cash',
        vendeur: 'Gestionnaire',
        notes: 'Paiement Mobile Money - Airtel'
      }
    ];

    await this.setSales(sampleSales);
    console.log('✅ Sample sales data initialized');
  }

  /**
   * Initialize sample expenses data
   */
  private async initializeSampleExpenses() {
    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const sampleExpenses = [
      {
        id: '1',
        numeroRecu: 'EX-' + now.toISOString().slice(0, 10).replace(/-/g, '') + '-0001',
        dateDepense: lastWeek.toISOString(),
        description: 'Achat de fournitures de bureau',
        montantCDF: 280000,
        montantUSD: 100,
        categorie: 'Fournitures',
        methodePaiement: 'cash',
        creePar: 'Super Admin',
        notes: 'Papier, stylos, et autres fournitures',
        dateCreation: lastWeek.toISOString(),
        dateModification: lastWeek.toISOString()
      },
      {
        id: '2',
        numeroRecu: 'EX-' + now.toISOString().slice(0, 10).replace(/-/g, '') + '-0002',
        dateDepense: now.toISOString(),
        description: 'Frais de transport - Livraison',
        montantCDF: 140000,
        montantUSD: 50,
        categorie: 'Transport',
        methodePaiement: 'mobile_money',
        creePar: 'Gestionnaire',
        notes: 'Transport pour livraison clients',
        dateCreation: now.toISOString(),
        dateModification: now.toISOString()
      }
    ];

    await this.setExpenses(sampleExpenses);
    console.log('✅ Sample expenses data initialized');
  }

  // Force initialize debt data
  async forceInitializeDebts() {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    const sampleDebts = [
      {
        id: 'DET-001',
        venteId: 'VTE-CREDIT-001',
        nomClient: 'Jean Baptiste Mukendi',
        telephoneClient: '+243 812 345 678',
        adresseClient: 'Avenue Lumumba, Kinshasa',
        montantTotalCDF: 150000,
        montantTotalUSD: 53.57,
        montantPayeCDF: 0,
        montantPayeUSD: 0,
        montantRestantCDF: 150000,
        montantRestantUSD: 53.57,
        dateCreation: oneWeekAgo.toISOString(),
        dateEcheance: oneMonthFromNow.toISOString(),
        statut: 'active',
        statutPaiement: 'impaye',
        paiements: [],
        notes: 'Vente à crédit - Produits électroniques'
      },
      {
        id: 'DET-002',
        venteId: 'VTE-CREDIT-002',
        nomClient: 'Marie Kabila Tshisekedi',
        telephoneClient: '+243 823 456 789',
        adresseClient: 'Boulevard du 30 Juin, Kinshasa',
        montantTotalCDF: 75000,
        montantTotalUSD: 26.79,
        montantPayeCDF: 25000,
        montantPayeUSD: 8.93,
        montantRestantCDF: 50000,
        montantRestantUSD: 17.86,
        dateCreation: oneWeekAgo.toISOString(),
        dateEcheance: oneMonthFromNow.toISOString(),
        statut: 'active',
        statutPaiement: 'impaye',
        paiements: [
          {
            id: 'PAY-001',
            montantCDF: 25000,
            montantUSD: 8.93,
            methodePaiement: 'cash',
            datePaiement: now.toISOString(),
            notes: 'Paiement partiel'
          }
        ],
        notes: 'Dette partiellement payée'
      }
    ];

    await this.setDebts(sampleDebts);
  }

  // Export data for backup (CSV format for both platforms)
  async exportData() {
    if (isMobile()) {
      // Use CSV import/export service for mobile
      const { csvImportExportService } = await import('./csv-import-export');
      const result = await csvImportExportService.exportAllData();
      return result.data ? { csvData: result.data, exportDate: new Date().toISOString() } : {};
    } else {
      // Desktop now also returns CSV format
      return storageService.exportData();
    }
  }

  // Import data from backup (supports CSV format)
  async importData(data: any) {
    if (isMobile()) {
      // Handle CSV import for mobile
      if (data.csvData && typeof data.csvData === 'string') {
        const { csvImportExportService } = await import('./csv-import-export');
        // For now, we'll parse the CSV backup and import sections individually
        // This is a simplified implementation - full CSV import would need more work
        console.log('CSV import for mobile needs full implementation');
        return false;
      } else {
        // Handle legacy JSON format
        return storageService.importData(data);
      }
    } else {
      return storageService.importData(data);
    }
  }

  // Migration method to transfer data from desktop to mobile
  async migrateFromDesktop() {
    if (!isMobile()) {
      console.warn('Migration should only be called on mobile platform');
      return false;
    }

    try {
      // Check if CSV storage is empty
      const csvUsers = await csvStorageService.getUsers();
      if (csvUsers.length > 0) {
        console.log('CSV storage already has data, skipping migration');
        return true;
      }

      // Try to get data from localStorage (if available in mobile webview)
      const desktopData = {
        users: localStorage.getItem('smartboutique_users'),
        products: localStorage.getItem('smartboutique_products'),
        sales: localStorage.getItem('smartboutique_sales'),
        debts: localStorage.getItem('smartboutique_debts'),
        expenses: localStorage.getItem('smartboutique_expenses'),
        settings: localStorage.getItem('smartboutique_settings'),
        currentUser: localStorage.getItem('smartboutique_currentUser')
      };

      // Migrate data if available
      let migrated = false;
      if (desktopData.users) {
        await csvStorageService.setUsers(JSON.parse(desktopData.users));
        migrated = true;
      }
      if (desktopData.products) {
        await csvStorageService.setProducts(JSON.parse(desktopData.products));
        migrated = true;
      }
      if (desktopData.sales) {
        await csvStorageService.setSales(JSON.parse(desktopData.sales));
        migrated = true;
      }
      if (desktopData.debts) {
        await csvStorageService.setDebts(JSON.parse(desktopData.debts));
        migrated = true;
      }
      if (desktopData.expenses) {
        await csvStorageService.setExpenses(JSON.parse(desktopData.expenses));
        migrated = true;
      }
      if (desktopData.settings) {
        await csvStorageService.setSettings(JSON.parse(desktopData.settings));
        migrated = true;
      }
      if (desktopData.currentUser) {
        await csvStorageService.setCurrentUser(JSON.parse(desktopData.currentUser));
        migrated = true;
      }

      if (migrated) {
        console.log('Successfully migrated data from desktop to mobile');
      } else {
        console.log('No desktop data found to migrate');
      }

      return true;
    } catch (error) {
      console.error('Error during migration:', error);
      return false;
    }
  }

  // Employee management methods
  async getEmployees() {
    await this.checkMigration();

    let employees: any[] = [];

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          employees = await this.sqliteStorage.getEmployees();
        }
      } catch (error) {
        console.warn('Mobile SQLite failed for employees, falling back to CSV:', error);
      }
      // For mobile, use CSV storage fallback
      if (employees.length === 0) {
        employees = csvStorageService.getEmployees ? await csvStorageService.getEmployees() : [];
      }
    } else {
      // For desktop, try SQLite first, fallback to localStorage storage
      try {
        employees = sqliteStorageService.getEmployees();
      } catch (error) {
        console.warn('SQLite not available for employees, falling back to localStorage storage:', error);
        // Fallback to localStorage storage for desktop when SQLite is not available
        employees = storageService.getEmployees ? storageService.getEmployees() : [];
      }
    }

    // Migrate employees from old nom/prenom structure to new nomComplet structure
    return this.migrateEmployeeStructure(employees);
  }

  // Helper method to migrate employee data structure from nom/prenom to nomComplet
  private migrateEmployeeStructure(employees: any[]): any[] {
    return employees.map(employee => {
      // If employee has old structure (nom/prenom), convert to new structure (nomComplet)
      if (employee.nom && !employee.nomComplet) {
        const nomComplet = employee.prenom
          ? `${employee.prenom} ${employee.nom}`
          : employee.nom;

        return {
          ...employee,
          nomComplet,
          // Remove old fields to avoid confusion
          nom: undefined,
          prenom: undefined
        };
      }

      // Employee already has new structure
      return employee;
    });
  }

  async addEmployee(employee: any) {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          await this.sqliteStorage.addEmployee(employee);
          return;
        }
      } catch (error) {
        console.warn('Mobile SQLite failed for employees, falling back to CSV:', error);
      }
      // For mobile, use CSV storage fallback
      if (csvStorageService.addEmployee) {
        await csvStorageService.addEmployee(employee);
      } else {
        throw new Error('Employee management not yet implemented for mobile CSV storage');
      }
    } else {
      // For desktop, try SQLite first, fallback to localStorage storage
      try {
        sqliteStorageService.addEmployee(employee);
      } catch (error) {
        console.warn('SQLite not available for employees, falling back to localStorage storage:', error);
        // Fallback to localStorage storage for desktop when SQLite is not available
        if (storageService.addEmployee) {
          storageService.addEmployee(employee);
        } else {
          throw new Error('Neither SQLite nor localStorage storage available for employees');
        }
      }
    }
  }

  async updateEmployee(employee: any) {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          await this.sqliteStorage.updateEmployee(employee);
          return;
        }
      } catch (error) {
        console.warn('Mobile SQLite failed for employees, falling back to CSV:', error);
      }
      // For mobile, use CSV storage fallback
      if (csvStorageService.updateEmployee) {
        await csvStorageService.updateEmployee(employee);
      } else {
        throw new Error('Employee management not yet implemented for mobile CSV storage');
      }
    } else {
      // For desktop, try SQLite first, fallback to localStorage storage
      try {
        sqliteStorageService.updateEmployee(employee);
      } catch (error) {
        console.warn('SQLite not available for employees, falling back to localStorage storage:', error);
        // Fallback to localStorage storage for desktop when SQLite is not available
        if (storageService.updateEmployee) {
          storageService.updateEmployee(employee);
        } else {
          throw new Error('Neither SQLite nor localStorage storage available for employees');
        }
      }
    }
  }

  async deleteEmployee(id: string) {
    await this.checkMigration();

    if (isMobile()) {
      // Try SQLite first, fallback to CSV if migration failed
      try {
        if (await mobileStorageMigrationService.isMigrationCompleted()) {
          await this.sqliteStorage.deleteEmployee(id);
          return;
        }
      } catch (error) {
        console.warn('Mobile SQLite failed for employees, falling back to CSV:', error);
      }
      // For mobile, use CSV storage fallback
      if (csvStorageService.deleteEmployee) {
        await csvStorageService.deleteEmployee(id);
      } else {
        throw new Error('Employee management not yet implemented for mobile CSV storage');
      }
    } else {
      // For desktop, try SQLite first, fallback to localStorage storage
      try {
        sqliteStorageService.deleteEmployee(id);
      } catch (error) {
        console.warn('SQLite not available for employees, falling back to localStorage storage:', error);
        // Fallback to localStorage storage for desktop when SQLite is not available
        if (storageService.deleteEmployee) {
          storageService.deleteEmployee(id);
        } else {
          throw new Error('Neither SQLite nor localStorage storage available for employees');
        }
      }
    }
  }
}

export const adaptiveStorageService = new AdaptiveStorageService();

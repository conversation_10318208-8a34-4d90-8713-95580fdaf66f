/**
 * Revenue and Profit Calculation Utilities for SmartBoutique
 * Updated to use standardized currency utilities
 * Handles profit margin calculations and revenue analytics
 */

import { Product, Sale } from '@/types';
import {
  roundCurrency,
  convertCDFToUSD,
  calculateBeneficeUnitaire,
  calculateBenefice,
  calculateChiffresAffaires,
  calculateMargeProfit,
  validatePricing as validatePricingCore,
  safeCalculation,
  DEFAULT_EXCHANGE_RATE
} from './currencyUtils.js';

/**
 * Calculate profit margin for a single product using standardized utilities
 */
export const calculateProductProfit = (
  prixAchatCDF: number,
  prixVenteCDF: number,
  exchangeRate: number = DEFAULT_EXCHANGE_RATE
): { beneficeUnitaireCDF: number; beneficeUnitaireUSD: number } => {
  const beneficeUnitaireCDF = roundCurrency(calculateBeneficeUnitaire(prixVenteCDF, prixAchatCDF));
  const beneficeUnitaireUSD = roundCurrency(convertCDFToUSD(beneficeUnitaireCDF, exchangeRate));

  return {
    beneficeUnitaireCDF,
    beneficeUnitaireUSD
  };
};

/**
 * Calculate total inventory revenue (potential profit from all stock) using standardized utilities
 */
export const calculateInventoryRevenue = (
  products: Product[]
): { totalRevenueCDF: number; totalRevenueUSD: number } => {
  return safeCalculation(() => {
    let totalRevenueCDF = 0;
    let totalRevenueUSD = 0;

    products.forEach(product => {
      // Calculate potential profit directly from current prices instead of relying on stored benefice fields
      if (product.prixCDF && product.prixAchatCDF && product.stock > 0) {
        const unitProfitCDF = product.prixCDF - product.prixAchatCDF;
        if (unitProfitCDF > 0) { // Only count positive profits
          totalRevenueCDF += roundCurrency(unitProfitCDF * product.stock);
        }
      }

      // Calculate USD profit using exchange rate
      if (product.prixUSD && product.prixAchatUSD && product.stock > 0) {
        const unitProfitUSD = product.prixUSD - product.prixAchatUSD;
        if (unitProfitUSD > 0) { // Only count positive profits
          totalRevenueUSD += roundCurrency(unitProfitUSD * product.stock);
        }
      } else if (product.prixCDF && product.prixAchatCDF && product.stock > 0) {
        // Fallback: calculate USD from CDF using exchange rate
        const unitProfitCDF = product.prixCDF - product.prixAchatCDF;
        if (unitProfitCDF > 0) {
          const unitProfitUSD = convertCDFToUSD(unitProfitCDF, DEFAULT_EXCHANGE_RATE);
          totalRevenueUSD += roundCurrency(unitProfitUSD * product.stock);
        }
      }
    });

    return {
      totalRevenueCDF: roundCurrency(totalRevenueCDF),
      totalRevenueUSD: roundCurrency(totalRevenueUSD)
    };
  }, { totalRevenueCDF: 0, totalRevenueUSD: 0 }, 'Erreur lors du calcul des revenus d\'inventaire');
};

/**
 * Calculate revenue breakdown by category
 */
export const calculateRevenueByCategory = (
  products: Product[]
): { [category: string]: { revenueCDF: number; revenueUSD: number; productCount: number } } => {
  const categoryRevenue: { [category: string]: { revenueCDF: number; revenueUSD: number; productCount: number } } = {};

  products.forEach(product => {
    if (!categoryRevenue[product.categorie]) {
      categoryRevenue[product.categorie] = {
        revenueCDF: 0,
        revenueUSD: 0,
        productCount: 0
      };
    }

    // Calculate potential profit directly from current prices
    if (product.prixCDF && product.prixAchatCDF && product.stock > 0) {
      const unitProfitCDF = product.prixCDF - product.prixAchatCDF;
      if (unitProfitCDF > 0) {
        categoryRevenue[product.categorie].revenueCDF += roundCurrency(unitProfitCDF * product.stock);
        categoryRevenue[product.categorie].productCount += 1;
      }
    }

    // Calculate USD profit
    if (product.prixUSD && product.prixAchatUSD && product.stock > 0) {
      const unitProfitUSD = product.prixUSD - product.prixAchatUSD;
      if (unitProfitUSD > 0) {
        categoryRevenue[product.categorie].revenueUSD += roundCurrency(unitProfitUSD * product.stock);
      }
    } else if (product.prixCDF && product.prixAchatCDF && product.stock > 0) {
      // Fallback: calculate USD from CDF
      const unitProfitCDF = product.prixCDF - product.prixAchatCDF;
      if (unitProfitCDF > 0) {
        const unitProfitUSD = convertCDFToUSD(unitProfitCDF, DEFAULT_EXCHANGE_RATE);
        categoryRevenue[product.categorie].revenueUSD += roundCurrency(unitProfitUSD * product.stock);
      }
    }
  });

  return categoryRevenue;
};

/**
 * Get top performing products by profit margin
 */
export const getTopPerformingProducts = (
  products: Product[],
  limit: number = 10
): Product[] => {
  return products
    .filter(product => product.prixCDF && product.prixAchatCDF && product.stock > 0)
    .filter(product => (product.prixCDF - product.prixAchatCDF) > 0) // Only profitable products
    .sort((a, b) => {
      const profitA = (a.prixCDF - a.prixAchatCDF) * a.stock;
      const profitB = (b.prixCDF - b.prixAchatCDF) * b.stock;
      return profitB - profitA;
    })
    .slice(0, limit);
};

/**
 * Calculate profit margin percentage for a product (profit over selling price)
 * This is the true profit margin, not markup percentage
 */
export const calculateProfitMarginPercentage = (
  prixAchatCDF: number,
  prixVenteCDF: number
): number => {
  if (prixVenteCDF === 0) return 0;
  return ((prixVenteCDF - prixAchatCDF) / prixVenteCDF) * 100;
};

/**
 * Calculate markup percentage for a product (profit over cost price)
 * This shows how much markup is applied over the cost
 */
export const calculateMarkupPercentage = (
  prixAchatCDF: number,
  prixVenteCDF: number
): number => {
  if (prixAchatCDF === 0) return 0;
  return ((prixVenteCDF - prixAchatCDF) / prixAchatCDF) * 100;
};

/**
 * Calculate realized revenue from actual sales using standardized utilities
 */
export const calculateRealizedRevenue = (
  sales: Sale[],
  products: Product[]
): { realizedRevenueCDF: number; realizedRevenueUSD: number } => {
  return safeCalculation(() => {
    let realizedRevenueCDF = 0;
    let realizedRevenueUSD = 0;

    sales.forEach(sale => {
      sale.produits.forEach(saleItem => {
        const product = products.find(p => p.id === saleItem.produitId);
        if (product && saleItem.prixUnitaireCDF && product.prixAchatCDF) {
          // Calculate actual profit from sale: sale price - purchase price
          const actualProfitCDF = saleItem.prixUnitaireCDF - product.prixAchatCDF;
          if (actualProfitCDF > 0) { // Only count positive profits
            realizedRevenueCDF += roundCurrency(actualProfitCDF * saleItem.quantite);
          }
        }

        // Calculate USD profit
        if (product && saleItem.prixUnitaireUSD && product.prixAchatUSD) {
          const actualProfitUSD = saleItem.prixUnitaireUSD - product.prixAchatUSD;
          if (actualProfitUSD > 0) { // Only count positive profits
            realizedRevenueUSD += roundCurrency(actualProfitUSD * saleItem.quantite);
          }
        } else if (product && saleItem.prixUnitaireCDF && product.prixAchatCDF) {
          // Fallback: calculate USD profit from CDF using exchange rate
          const actualProfitCDF = saleItem.prixUnitaireCDF - product.prixAchatCDF;
          if (actualProfitCDF > 0) {
            const actualProfitUSD = convertCDFToUSD(actualProfitCDF, DEFAULT_EXCHANGE_RATE);
            realizedRevenueUSD += roundCurrency(actualProfitUSD * saleItem.quantite);
          }
        }
      });
    });

    return {
      realizedRevenueCDF: roundCurrency(realizedRevenueCDF),
      realizedRevenueUSD: roundCurrency(realizedRevenueUSD)
    };
  }, { realizedRevenueCDF: 0, realizedRevenueUSD: 0 }, 'Erreur lors du calcul des revenus réalisés');
};

/**
 * Validate that selling price is greater than purchase price using standardized validation
 * This function is now a wrapper around the centralized validation
 */
export const validatePricing = (
  prixAchatCDF: number,
  prixVenteCDF: number
): { isValid: boolean; errorMessage?: string } => {
  const validation = validatePricingCore(prixAchatCDF, prixVenteCDF);

  return {
    isValid: validation.isValid,
    errorMessage: validation.errors.length > 0 ? validation.errors[0] : undefined
  };
};

/**
 * Format revenue display with currency
 */
export const formatRevenue = (
  amount: number,
  currency: 'CDF' | 'USD'
): string => {
  if (currency === 'USD') {
    return `$${amount.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  }
  return `${amount.toLocaleString('fr-FR')} CDF`;
};

/**
 * Revenue analytics interface for dashboard
 */
export interface RevenueAnalytics {
  totalInventoryRevenueCDF: number;
  totalInventoryRevenueUSD: number;
  realizedRevenueCDF: number;
  realizedRevenueUSD: number;
  categoryBreakdown: { [category: string]: { revenueCDF: number; revenueUSD: number; productCount: number } };
  topProducts: Product[];
  totalProductsWithProfit: number; // Count of products with valid pricing and positive profit
}

/**
 * Generate comprehensive revenue analytics
 */
export const generateRevenueAnalytics = (
  products: Product[],
  sales: Sale[]
): RevenueAnalytics => {
  // Debug logging to help identify data issues
  console.log('🔍 Revenue Analytics Debug:', {
    totalProducts: products.length,
    totalSales: sales.length,
    productsWithPricing: products.filter(p => p.prixCDF && p.prixAchatCDF).length,
    productsWithStock: products.filter(p => p.stock > 0).length,
    profitableProducts: products.filter(p => p.prixCDF && p.prixAchatCDF && (p.prixCDF - p.prixAchatCDF) > 0).length
  });

  const inventoryRevenue = calculateInventoryRevenue(products);
  const realizedRevenue = calculateRealizedRevenue(sales, products);
  const categoryBreakdown = calculateRevenueByCategory(products);
  const topProducts = getTopPerformingProducts(products, 5);

  // Calculate total products with profit for dashboard validation
  const totalProductsWithProfit = products.filter(p =>
    p.prixCDF && p.prixAchatCDF && (p.prixCDF - p.prixAchatCDF) > 0
  ).length;

  console.log('💰 Calculated Revenue:', {
    potentialProfitCDF: inventoryRevenue.totalRevenueCDF,
    realizedProfitCDF: realizedRevenue.realizedRevenueCDF,
    topProductsCount: topProducts.length,
    totalProductsWithProfit
  });

  return {
    totalInventoryRevenueCDF: inventoryRevenue.totalRevenueCDF,
    totalInventoryRevenueUSD: inventoryRevenue.totalRevenueUSD,
    realizedRevenueCDF: realizedRevenue.realizedRevenueCDF,
    realizedRevenueUSD: realizedRevenue.realizedRevenueUSD,
    categoryBreakdown,
    topProducts,
    totalProductsWithProfit
  };
};

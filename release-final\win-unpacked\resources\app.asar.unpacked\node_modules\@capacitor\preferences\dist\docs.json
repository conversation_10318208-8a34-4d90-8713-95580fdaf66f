{"api": {"name": "PreferencesPlugin", "slug": "preferencesplugin", "docs": "", "tags": [], "methods": [{"name": "configure", "signature": "(options: ConfigureOptions) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "ConfigureOptions"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Configure the preferences plugin at runtime.\n\nOptions that are `undefined` will not be used.", "complexTypes": ["ConfigureOptions"], "slug": "configure"}, {"name": "get", "signature": "(options: GetOptions) => Promise<GetResult>", "parameters": [{"name": "options", "docs": "", "type": "GetOptions"}], "returns": "Promise<GetResult>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Get the value from preferences of a given key.", "complexTypes": ["GetResult", "GetOptions"], "slug": "get"}, {"name": "set", "signature": "(options: SetOptions) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "SetOptions"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Set the value in preferences for a given key.", "complexTypes": ["SetOptions"], "slug": "set"}, {"name": "remove", "signature": "(options: RemoveOptions) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "RemoveOptions"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Remove the value from preferences for a given key, if any.", "complexTypes": ["RemoveOptions"], "slug": "remove"}, {"name": "clear", "signature": "() => Promise<void>", "parameters": [], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Clear keys and values from preferences.", "complexTypes": [], "slug": "clear"}, {"name": "keys", "signature": "() => Promise<KeysResult>", "parameters": [], "returns": "Promise<KeysResult>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Return the list of known keys in preferences.", "complexTypes": ["KeysResult"], "slug": "keys"}, {"name": "migrate", "signature": "() => Promise<MigrateResult>", "parameters": [], "returns": "Promise<MigrateResult>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Migrate data from the Capacitor 2 Storage plugin.\n\nThis action is non-destructive. It will not remove old data and will only\nwrite new data if they key was not already set.\nTo remove the old data after being migrated, call removeOld().", "complexTypes": ["MigrateResult"], "slug": "migrate"}, {"name": "removeOld", "signature": "() => Promise<void>", "parameters": [], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.1.0"}], "docs": "Removes old data with `_cap_` prefix from the Capacitor 2 Storage plugin.", "complexTypes": [], "slug": "removeold"}], "properties": []}, "interfaces": [{"name": "ConfigureOptions", "slug": "configureoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "group", "tags": [{"text": "CapacitorStorage", "name": "default"}, {"text": "1.0.0", "name": "since"}], "docs": "Set the preferences group.\n\nPreferences groups are used to organize key/value pairs.\n\nUsing the value 'NativeStorage' provides backwards-compatibility with\n[`cordova-plugin-nativestorage`](https://www.npmjs.com/package/cordova-plugin-nativestorage).\nWARNING: The `clear()` method can delete unintended values when using the\n'NativeStorage' group.", "complexTypes": [], "type": "string | undefined"}]}, {"name": "GetResult", "slug": "getresult", "docs": "", "tags": [], "methods": [], "properties": [{"name": "value", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The value from preferences associated with the given key.\n\nIf a value was not previously set or was removed, value will be `null`.", "complexTypes": [], "type": "string | null"}]}, {"name": "GetOptions", "slug": "getoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "key", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The key whose value to retrieve from preferences.", "complexTypes": [], "type": "string"}]}, {"name": "SetOptions", "slug": "setoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "key", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The key to associate with the value being set in preferences.", "complexTypes": [], "type": "string"}, {"name": "value", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The value to set in preferences with the associated key.", "complexTypes": [], "type": "string"}]}, {"name": "RemoveOptions", "slug": "removeoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "key", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The key whose value to remove from preferences.", "complexTypes": [], "type": "string"}]}, {"name": "KeysResult", "slug": "<PERSON><PERSON><PERSON>", "docs": "", "tags": [], "methods": [], "properties": [{"name": "keys", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "The known keys in preferences.", "complexTypes": [], "type": "string[]"}]}, {"name": "MigrateResult", "slug": "<PERSON><PERSON><PERSON>", "docs": "", "tags": [], "methods": [], "properties": [{"name": "migrated", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "An array of keys that were migrated.", "complexTypes": [], "type": "string[]"}, {"name": "existing", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "An array of keys that were already migrated or otherwise exist in preferences\nthat had a value in the Capacitor 2 Preferences plugin.", "complexTypes": [], "type": "string[]"}]}], "enums": [], "typeAliases": [], "pluginConfigs": []}
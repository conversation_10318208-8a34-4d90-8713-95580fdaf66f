{"name": "@capacitor/camera", "version": "7.0.1", "description": "The Camera API provides the ability to take a photo with the camera or choose an existing one from the photo album.", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Sources", "ios/Tests", "Package.swift", "CapacitorCamera.podspec"], "author": "Ionic <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/capacitor-plugins.git"}, "devDependencies": {"@capacitor/android": "next", "@capacitor/core": "next", "@capacitor/docgen": "0.2.2", "@capacitor/ios": "next", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "~1.0.1", "@ionic/swiftlint-config": "^1.1.2", "eslint": "^8.57.0", "prettier": "~2.3.0", "prettier-plugin-java": "~1.0.2", "rimraf": "^6.0.1", "rollup": "^4.26.0", "swiftlint": "^1.0.1", "typescript": "~4.1.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}, "publishConfig": {"access": "public"}}